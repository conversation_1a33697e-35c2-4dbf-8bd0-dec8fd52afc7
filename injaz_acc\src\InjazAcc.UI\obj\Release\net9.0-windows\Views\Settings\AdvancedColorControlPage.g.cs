﻿#pragma checksum "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A1828512130A48EF5221664A7799F3E1DD2C4B89"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Settings;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Settings {
    
    
    /// <summary>
    /// AdvancedColorControlPage
    /// </summary>
    public partial class AdvancedColorControlPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 53 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrimaryColor;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPrimaryColor;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrimaryDarkColor;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPrimaryDarkColor;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrimaryLightColor;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPrimaryLightColor;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryColor;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryColor;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryDarkColor;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryDarkColor;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryLightColor;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryLightColor;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAccentColor;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAccentColor;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSurfaceColor;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSurfaceColor;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCardBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCardBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDialogBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDialogBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSidebarBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSidebarBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnHeaderBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHeaderBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnFooterBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtFooterBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrimaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPrimaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDisabledTextColor;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDisabledTextColor;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnHintTextColor;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHintTextColor;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOnPrimaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOnPrimaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOnSecondaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOnSecondaryTextColor;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOnSurfaceTextColor;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOnSurfaceTextColor;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOnBackgroundTextColor;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOnBackgroundTextColor;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBorderColor;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtBorderColor;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDividerColor;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDividerColor;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnOutlineColor;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOutlineColor;
        
        #line default
        #line hidden
        
        
        #line 374 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnFocusBorderColor;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtFocusBorderColor;
        
        #line default
        #line hidden
        
        
        #line 384 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnErrorBorderColor;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtErrorBorderColor;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnButtonBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtButtonBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 428 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnButtonTextColor;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtButtonTextColor;
        
        #line default
        #line hidden
        
        
        #line 438 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnButtonHoverColor;
        
        #line default
        #line hidden
        
        
        #line 440 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtButtonHoverColor;
        
        #line default
        #line hidden
        
        
        #line 448 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnButtonPressedColor;
        
        #line default
        #line hidden
        
        
        #line 450 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtButtonPressedColor;
        
        #line default
        #line hidden
        
        
        #line 458 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnButtonDisabledColor;
        
        #line default
        #line hidden
        
        
        #line 460 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtButtonDisabledColor;
        
        #line default
        #line hidden
        
        
        #line 468 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnButtonDisabledTextColor;
        
        #line default
        #line hidden
        
        
        #line 470 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtButtonDisabledTextColor;
        
        #line default
        #line hidden
        
        
        #line 493 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryButtonBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryButtonBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 503 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryButtonTextColor;
        
        #line default
        #line hidden
        
        
        #line 505 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryButtonTextColor;
        
        #line default
        #line hidden
        
        
        #line 513 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryButtonHoverColor;
        
        #line default
        #line hidden
        
        
        #line 515 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryButtonHoverColor;
        
        #line default
        #line hidden
        
        
        #line 523 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSecondaryButtonPressedColor;
        
        #line default
        #line hidden
        
        
        #line 525 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSecondaryButtonPressedColor;
        
        #line default
        #line hidden
        
        
        #line 559 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSelectionColor;
        
        #line default
        #line hidden
        
        
        #line 561 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSelectionColor;
        
        #line default
        #line hidden
        
        
        #line 569 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnHoverColor;
        
        #line default
        #line hidden
        
        
        #line 571 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHoverColor;
        
        #line default
        #line hidden
        
        
        #line 579 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnFocusColor;
        
        #line default
        #line hidden
        
        
        #line 581 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtFocusColor;
        
        #line default
        #line hidden
        
        
        #line 589 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnActiveColor;
        
        #line default
        #line hidden
        
        
        #line 591 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtActiveColor;
        
        #line default
        #line hidden
        
        
        #line 599 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnHighlightColor;
        
        #line default
        #line hidden
        
        
        #line 601 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHighlightColor;
        
        #line default
        #line hidden
        
        
        #line 609 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSuccessColor;
        
        #line default
        #line hidden
        
        
        #line 611 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSuccessColor;
        
        #line default
        #line hidden
        
        
        #line 619 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSuccessLightColor;
        
        #line default
        #line hidden
        
        
        #line 621 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSuccessLightColor;
        
        #line default
        #line hidden
        
        
        #line 629 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSuccessDarkColor;
        
        #line default
        #line hidden
        
        
        #line 631 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSuccessDarkColor;
        
        #line default
        #line hidden
        
        
        #line 639 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnWarningColor;
        
        #line default
        #line hidden
        
        
        #line 641 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtWarningColor;
        
        #line default
        #line hidden
        
        
        #line 649 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnWarningLightColor;
        
        #line default
        #line hidden
        
        
        #line 651 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtWarningLightColor;
        
        #line default
        #line hidden
        
        
        #line 659 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnWarningDarkColor;
        
        #line default
        #line hidden
        
        
        #line 661 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtWarningDarkColor;
        
        #line default
        #line hidden
        
        
        #line 669 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnErrorColor;
        
        #line default
        #line hidden
        
        
        #line 671 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtErrorColor;
        
        #line default
        #line hidden
        
        
        #line 679 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnErrorLightColor;
        
        #line default
        #line hidden
        
        
        #line 681 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtErrorLightColor;
        
        #line default
        #line hidden
        
        
        #line 689 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnErrorDarkColor;
        
        #line default
        #line hidden
        
        
        #line 691 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtErrorDarkColor;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/settings/advancedcolorcontrolpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnPrimaryColor = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnPrimaryColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.txtPrimaryColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 57 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtPrimaryColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnPrimaryDarkColor = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnPrimaryDarkColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txtPrimaryDarkColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 67 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtPrimaryDarkColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnPrimaryLightColor = ((System.Windows.Controls.Button)(target));
            
            #line 73 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnPrimaryLightColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.txtPrimaryLightColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 77 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtPrimaryLightColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnSecondaryColor = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.txtSecondaryColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 87 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btnSecondaryDarkColor = ((System.Windows.Controls.Button)(target));
            
            #line 93 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryDarkColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.txtSecondaryDarkColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 97 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryDarkColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnSecondaryLightColor = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryLightColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.txtSecondaryLightColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 107 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryLightColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.btnAccentColor = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnAccentColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.txtAccentColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 117 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtAccentColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btnBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 147 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.txtBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 151 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btnSurfaceColor = ((System.Windows.Controls.Button)(target));
            
            #line 157 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSurfaceColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.txtSurfaceColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 161 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSurfaceColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.btnCardBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 167 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnCardBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.txtCardBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 171 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtCardBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 21:
            this.btnDialogBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnDialogBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.txtDialogBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 181 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtDialogBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 23:
            this.btnSidebarBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSidebarBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.txtSidebarBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 191 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSidebarBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 25:
            this.btnHeaderBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 197 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnHeaderBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.txtHeaderBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 201 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtHeaderBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 27:
            this.btnFooterBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 207 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnFooterBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.txtFooterBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 211 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtFooterBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 29:
            this.btnPrimaryTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 241 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnPrimaryTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.txtPrimaryTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 245 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtPrimaryTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 31:
            this.btnSecondaryTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 251 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.txtSecondaryTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 255 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 33:
            this.btnDisabledTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 261 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnDisabledTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.txtDisabledTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 265 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtDisabledTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 35:
            this.btnHintTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 271 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnHintTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.txtHintTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 275 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtHintTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 37:
            this.btnOnPrimaryTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 281 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnOnPrimaryTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.txtOnPrimaryTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 285 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtOnPrimaryTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 39:
            this.btnOnSecondaryTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 291 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnOnSecondaryTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.txtOnSecondaryTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 295 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtOnSecondaryTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 41:
            this.btnOnSurfaceTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 301 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnOnSurfaceTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.txtOnSurfaceTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 305 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtOnSurfaceTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 43:
            this.btnOnBackgroundTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 311 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnOnBackgroundTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.txtOnBackgroundTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 315 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtOnBackgroundTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 45:
            this.btnBorderColor = ((System.Windows.Controls.Button)(target));
            
            #line 344 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnBorderColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.txtBorderColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 348 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtBorderColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 47:
            this.btnDividerColor = ((System.Windows.Controls.Button)(target));
            
            #line 354 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnDividerColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.txtDividerColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 358 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtDividerColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 49:
            this.btnOutlineColor = ((System.Windows.Controls.Button)(target));
            
            #line 364 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnOutlineColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            this.txtOutlineColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 368 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtOutlineColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 51:
            this.btnFocusBorderColor = ((System.Windows.Controls.Button)(target));
            
            #line 374 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnFocusBorderColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 52:
            this.txtFocusBorderColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 378 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtFocusBorderColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 53:
            this.btnErrorBorderColor = ((System.Windows.Controls.Button)(target));
            
            #line 384 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnErrorBorderColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 54:
            this.txtErrorBorderColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 388 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtErrorBorderColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 55:
            this.btnButtonBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 418 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnButtonBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.txtButtonBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 422 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtButtonBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 57:
            this.btnButtonTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 428 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnButtonTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 58:
            this.txtButtonTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 432 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtButtonTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 59:
            this.btnButtonHoverColor = ((System.Windows.Controls.Button)(target));
            
            #line 438 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnButtonHoverColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 60:
            this.txtButtonHoverColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 442 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtButtonHoverColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 61:
            this.btnButtonPressedColor = ((System.Windows.Controls.Button)(target));
            
            #line 448 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnButtonPressedColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            this.txtButtonPressedColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 452 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtButtonPressedColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 63:
            this.btnButtonDisabledColor = ((System.Windows.Controls.Button)(target));
            
            #line 458 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnButtonDisabledColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 64:
            this.txtButtonDisabledColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 462 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtButtonDisabledColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 65:
            this.btnButtonDisabledTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 468 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnButtonDisabledTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 66:
            this.txtButtonDisabledTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 472 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtButtonDisabledTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 67:
            this.btnSecondaryButtonBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 493 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryButtonBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 68:
            this.txtSecondaryButtonBackgroundColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 497 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryButtonBackgroundColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 69:
            this.btnSecondaryButtonTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 503 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryButtonTextColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 70:
            this.txtSecondaryButtonTextColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 507 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryButtonTextColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 71:
            this.btnSecondaryButtonHoverColor = ((System.Windows.Controls.Button)(target));
            
            #line 513 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryButtonHoverColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 72:
            this.txtSecondaryButtonHoverColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 517 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryButtonHoverColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 73:
            this.btnSecondaryButtonPressedColor = ((System.Windows.Controls.Button)(target));
            
            #line 523 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSecondaryButtonPressedColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 74:
            this.txtSecondaryButtonPressedColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 527 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSecondaryButtonPressedColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 75:
            this.btnSelectionColor = ((System.Windows.Controls.Button)(target));
            
            #line 559 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSelectionColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 76:
            this.txtSelectionColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 563 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSelectionColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 77:
            this.btnHoverColor = ((System.Windows.Controls.Button)(target));
            
            #line 569 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnHoverColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 78:
            this.txtHoverColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 573 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtHoverColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 79:
            this.btnFocusColor = ((System.Windows.Controls.Button)(target));
            
            #line 579 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnFocusColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 80:
            this.txtFocusColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 583 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtFocusColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 81:
            this.btnActiveColor = ((System.Windows.Controls.Button)(target));
            
            #line 589 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnActiveColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 82:
            this.txtActiveColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 593 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtActiveColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 83:
            this.btnHighlightColor = ((System.Windows.Controls.Button)(target));
            
            #line 599 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnHighlightColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 84:
            this.txtHighlightColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 603 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtHighlightColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 85:
            this.btnSuccessColor = ((System.Windows.Controls.Button)(target));
            
            #line 609 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSuccessColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 86:
            this.txtSuccessColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 613 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSuccessColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 87:
            this.btnSuccessLightColor = ((System.Windows.Controls.Button)(target));
            
            #line 619 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSuccessLightColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 88:
            this.txtSuccessLightColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 623 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSuccessLightColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 89:
            this.btnSuccessDarkColor = ((System.Windows.Controls.Button)(target));
            
            #line 629 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnSuccessDarkColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 90:
            this.txtSuccessDarkColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 633 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtSuccessDarkColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 91:
            this.btnWarningColor = ((System.Windows.Controls.Button)(target));
            
            #line 639 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnWarningColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 92:
            this.txtWarningColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 643 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtWarningColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 93:
            this.btnWarningLightColor = ((System.Windows.Controls.Button)(target));
            
            #line 649 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnWarningLightColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 94:
            this.txtWarningLightColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 653 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtWarningLightColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 95:
            this.btnWarningDarkColor = ((System.Windows.Controls.Button)(target));
            
            #line 659 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnWarningDarkColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 96:
            this.txtWarningDarkColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 663 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtWarningDarkColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 97:
            this.btnErrorColor = ((System.Windows.Controls.Button)(target));
            
            #line 669 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnErrorColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 98:
            this.txtErrorColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 673 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtErrorColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 99:
            this.btnErrorLightColor = ((System.Windows.Controls.Button)(target));
            
            #line 679 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnErrorLightColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 100:
            this.txtErrorLightColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 683 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtErrorLightColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 101:
            this.btnErrorDarkColor = ((System.Windows.Controls.Button)(target));
            
            #line 689 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.btnErrorDarkColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 102:
            this.txtErrorDarkColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 693 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            this.txtErrorDarkColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 103:
            
            #line 715 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 104:
            
            #line 716 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 105:
            
            #line 717 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 106:
            
            #line 718 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 107:
            
            #line 719 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 108:
            
            #line 720 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 109:
            
            #line 721 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 110:
            
            #line 722 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyTheme_Click);
            
            #line default
            #line hidden
            return;
            case 111:
            
            #line 730 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSettings_Click);
            
            #line default
            #line hidden
            return;
            case 112:
            
            #line 731 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LoadSettings_Click);
            
            #line default
            #line hidden
            return;
            case 113:
            
            #line 732 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetSettings_Click);
            
            #line default
            #line hidden
            return;
            case 114:
            
            #line 733 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportColors_Click);
            
            #line default
            #line hidden
            return;
            case 115:
            
            #line 734 "..\..\..\..\..\Views\Settings\AdvancedColorControlPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ImportColors_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

