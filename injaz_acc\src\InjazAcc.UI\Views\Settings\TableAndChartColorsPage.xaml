<UserControl x:Class="InjazAcc.UI.Views.Settings.TableAndChartColorsPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             Background="{DynamicResource BackgroundBrush}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="ألوان الجداول والرسوم البيانية" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="{DynamicResource PrimaryTextBrush}"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="تخصيص ألوان الجداول والرسوم البيانية وألوان الأقسام" 
                          FontSize="14" 
                          Foreground="{DynamicResource SecondaryTextBrush}"
                          HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>

            <!-- المحتوى الرئيسي -->
            <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                
                <!-- تبويب ألوان الجداول -->
                <TabItem Header="ألوان الجداول">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <GroupBox Header="ألوان الجداول" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- رأس الجدول -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="رأس الجدول" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnTableHeaderColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource TableHeaderBrush}" Tag="TableHeaderColor"/>
                                        <TextBox x:Name="txtTableHeaderColor" Margin="0,5,0,0" 
                                                Text="{Binding TableHeaderColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="TableHeaderColor"/>
                                    </StackPanel>

                                    <!-- الصف الزوجي -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="الصف الزوجي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnTableRowEvenColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource TableRowEvenBrush}" Tag="TableRowEvenColor"/>
                                        <TextBox x:Name="txtTableRowEvenColor" Margin="0,5,0,0" 
                                                Text="{Binding TableRowEvenColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="TableRowEvenColor"/>
                                    </StackPanel>

                                    <!-- الصف الفردي -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="الصف الفردي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnTableRowOddColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource TableRowOddBrush}" Tag="TableRowOddColor"/>
                                        <TextBox x:Name="txtTableRowOddColor" Margin="0,5,0,0" 
                                                Text="{Binding TableRowOddColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="TableRowOddColor"/>
                                    </StackPanel>

                                    <!-- حدود الجدول -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="حدود الجدول" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnTableBorderColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource TableBorderBrush}" Tag="TableBorderColor"/>
                                        <TextBox x:Name="txtTableBorderColor" Margin="0,5,0,0" 
                                                Text="{Binding TableBorderColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="TableBorderColor"/>
                                    </StackPanel>

                                    <!-- الصف المحدد -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="الصف المحدد" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnTableSelectedRowColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource TableSelectedRowBrush}" Tag="TableSelectedRowColor"/>
                                        <TextBox x:Name="txtTableSelectedRowColor" Margin="0,5,0,0" 
                                                Text="{Binding TableSelectedRowColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="TableSelectedRowColor"/>
                                    </StackPanel>

                                    <!-- الصف عند التمرير -->
                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="الصف عند التمرير" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnTableHoverRowColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource TableHoverRowBrush}" Tag="TableHoverRowColor"/>
                                        <TextBox x:Name="txtTableHoverRowColor" Margin="0,5,0,0" 
                                                Text="{Binding TableHoverRowColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="TableHoverRowColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                            <!-- معاينة الجدول -->
                            <GroupBox Header="معاينة الجدول" Margin="0,0,0,20">
                                <DataGrid x:Name="previewDataGrid" Height="200" Margin="10" 
                                         AutoGenerateColumns="False" IsReadOnly="True"
                                         HeadersVisibility="Column" GridLinesVisibility="All">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80"/>
                                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                                        <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="120"/>
                                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount}" Width="100"/>
                                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="120"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب ألوان الرسوم البيانية -->
                <TabItem Header="ألوان الرسوم البيانية">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <GroupBox Header="ألوان الرسوم البيانية" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- اللون الأول -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="اللون الأول" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart1Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart1Brush}" Tag="Chart1Color"/>
                                        <TextBox x:Name="txtChart1Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart1Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart1Color"/>
                                    </StackPanel>

                                    <!-- اللون الثاني -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="اللون الثاني" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart2Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart2Brush}" Tag="Chart2Color"/>
                                        <TextBox x:Name="txtChart2Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart2Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart2Color"/>
                                    </StackPanel>

                                    <!-- اللون الثالث -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="اللون الثالث" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart3Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart3Brush}" Tag="Chart3Color"/>
                                        <TextBox x:Name="txtChart3Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart3Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart3Color"/>
                                    </StackPanel>

                                    <!-- اللون الرابع -->
                                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="5">
                                        <TextBlock Text="اللون الرابع" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart4Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart4Brush}" Tag="Chart4Color"/>
                                        <TextBox x:Name="txtChart4Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart4Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart4Color"/>
                                    </StackPanel>

                                    <!-- اللون الخامس -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="اللون الخامس" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart5Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart5Brush}" Tag="Chart5Color"/>
                                        <TextBox x:Name="txtChart5Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart5Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart5Color"/>
                                    </StackPanel>

                                    <!-- اللون السادس -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="اللون السادس" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart6Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart6Brush}" Tag="Chart6Color"/>
                                        <TextBox x:Name="txtChart6Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart6Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart6Color"/>
                                    </StackPanel>

                                    <!-- اللون السابع -->
                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="اللون السابع" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart7Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart7Brush}" Tag="Chart7Color"/>
                                        <TextBox x:Name="txtChart7Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart7Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart7Color"/>
                                    </StackPanel>

                                    <!-- اللون الثامن -->
                                    <StackPanel Grid.Row="1" Grid.Column="3" Margin="5">
                                        <TextBlock Text="اللون الثامن" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnChart8Color" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource Chart8Brush}" Tag="Chart8Color"/>
                                        <TextBox x:Name="txtChart8Color" Margin="0,5,0,0" 
                                                Text="{Binding Chart8Color, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="Chart8Color"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                            <!-- معاينة الرسم البياني -->
                            <GroupBox Header="معاينة الرسم البياني" Margin="0,0,0,20">
                                <Canvas x:Name="chartPreview" Height="200" Margin="10" Background="White">
                                    <!-- سيتم إضافة الرسم البياني هنا برمجياً -->
                                </Canvas>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب ألوان الأقسام -->
                <TabItem Header="ألوان الأقسام">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <GroupBox Header="ألوان أقسام البرنامج" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- المبيعات -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="المبيعات" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSalesColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SalesBrush}" Tag="SalesColor"/>
                                        <TextBox x:Name="txtSalesColor" Margin="0,5,0,0" 
                                                Text="{Binding SalesColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SalesColor"/>
                                    </StackPanel>

                                    <!-- المشتريات -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="المشتريات" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnPurchasesColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource PurchasesBrush}" Tag="PurchasesColor"/>
                                        <TextBox x:Name="txtPurchasesColor" Margin="0,5,0,0" 
                                                Text="{Binding PurchasesColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="PurchasesColor"/>
                                    </StackPanel>

                                    <!-- المخزون -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="المخزون" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnInventoryColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource InventoryBrush}" Tag="InventoryColor"/>
                                        <TextBox x:Name="txtInventoryColor" Margin="0,5,0,0" 
                                                Text="{Binding InventoryColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="InventoryColor"/>
                                    </StackPanel>

                                    <!-- العملاء -->
                                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="5">
                                        <TextBlock Text="العملاء" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnCustomersColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource CustomersBrush}" Tag="CustomersColor"/>
                                        <TextBox x:Name="txtCustomersColor" Margin="0,5,0,0" 
                                                Text="{Binding CustomersColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="CustomersColor"/>
                                    </StackPanel>

                                    <!-- الموردين -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="الموردين" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSuppliersColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SuppliersBrush}" Tag="SuppliersColor"/>
                                        <TextBox x:Name="txtSuppliersColor" Margin="0,5,0,0" 
                                                Text="{Binding SuppliersColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SuppliersColor"/>
                                    </StackPanel>

                                    <!-- المحاسبة -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="المحاسبة" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnAccountingColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource AccountingBrush}" Tag="AccountingColor"/>
                                        <TextBox x:Name="txtAccountingColor" Margin="0,5,0,0" 
                                                Text="{Binding AccountingColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="AccountingColor"/>
                                    </StackPanel>

                                    <!-- التقارير -->
                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="التقارير" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnReportsColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ReportsBrush}" Tag="ReportsColor"/>
                                        <TextBox x:Name="txtReportsColor" Margin="0,5,0,0" 
                                                Text="{Binding ReportsColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ReportsColor"/>
                                    </StackPanel>

                                    <!-- الإعدادات -->
                                    <StackPanel Grid.Row="1" Grid.Column="3" Margin="5">
                                        <TextBlock Text="الإعدادات" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSettingsColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SettingsBrush}" Tag="SettingsColor"/>
                                        <TextBox x:Name="txtSettingsColor" Margin="0,5,0,0" 
                                                Text="{Binding SettingsColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SettingsColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                            <!-- معاينة الأقسام -->
                            <GroupBox Header="معاينة ألوان الأقسام" Margin="0,0,0,20">
                                <WrapPanel Margin="10" Orientation="Horizontal">
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource SalesBrush}">
                                        <TextBlock Text="المبيعات" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource PurchasesBrush}">
                                        <TextBlock Text="المشتريات" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource InventoryBrush}">
                                        <TextBlock Text="المخزون" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource CustomersBrush}">
                                        <TextBlock Text="العملاء" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource SuppliersBrush}">
                                        <TextBlock Text="الموردين" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource AccountingBrush}">
                                        <TextBlock Text="المحاسبة" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource ReportsBrush}">
                                        <TextBlock Text="التقارير" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource SettingsBrush}">
                                        <TextBlock Text="الإعدادات" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                </WrapPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

            </TabControl>

        </Grid>
    </ScrollViewer>

</UserControl>