<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- ألوان النظام الأساسية -->
    <Color x:Key="PrimaryColor">#009688</Color>
    <Color x:Key="PrimaryDarkColor">#00695C</Color>
    <Color x:Key="PrimaryLightColor">#4DB6AC</Color>
    
    <Color x:Key="SecondaryColor">#FFC107</Color>
    <Color x:Key="SecondaryDarkColor">#FF8F00</Color>
    <Color x:Key="SecondaryLightColor">#FFECB3</Color>
    
    <Color x:Key="AccentColor">#FF4081</Color>
    
    <!-- ألوان الخلفية -->
    <Color x:Key="BackgroundColor">#FAFAFA</Color>
    <Color x:Key="SurfaceColor">#FFFFFF</Color>
    <Color x:Key="CardBackgroundColor">#FFFFFF</Color>
    <Color x:Key="DialogBackgroundColor">#FFFFFF</Color>
    <Color x:Key="SidebarBackgroundColor">#263238</Color>
    <Color x:Key="HeaderBackgroundColor">#FFFFFF</Color>
    <Color x:Key="FooterBackgroundColor">#F5F5F5</Color>
    
    <!-- ألوان النصوص -->
    <Color x:Key="PrimaryTextColor">#212121</Color>
    <Color x:Key="SecondaryTextColor">#757575</Color>
    <Color x:Key="DisabledTextColor">#BDBDBD</Color>
    <Color x:Key="HintTextColor">#9E9E9E</Color>
    <Color x:Key="OnPrimaryTextColor">#FFFFFF</Color>
    <Color x:Key="OnSecondaryTextColor">#000000</Color>
    <Color x:Key="OnSurfaceTextColor">#000000</Color>
    <Color x:Key="OnBackgroundTextColor">#000000</Color>
    
    <!-- ألوان الحدود -->
    <Color x:Key="BorderColor">#E0E0E0</Color>
    <Color x:Key="DividerColor">#EEEEEE</Color>
    <Color x:Key="OutlineColor">#BDBDBD</Color>
    <Color x:Key="FocusBorderColor">#2196F3</Color>
    <Color x:Key="ErrorBorderColor">#F44336</Color>
    
    <!-- ألوان الأزرار -->
    <Color x:Key="ButtonBackgroundColor">#009688</Color>
    <Color x:Key="ButtonTextColor">#FFFFFF</Color>
    <Color x:Key="ButtonHoverColor">#00796B</Color>
    <Color x:Key="ButtonPressedColor">#004D40</Color>
    <Color x:Key="ButtonDisabledColor">#E0E0E0</Color>
    <Color x:Key="ButtonDisabledTextColor">#9E9E9E</Color>
    
    <!-- ألوان الأزرار الثانوية -->
    <Color x:Key="SecondaryButtonBackgroundColor">#F5F5F5</Color>
    <Color x:Key="SecondaryButtonTextColor">#424242</Color>
    <Color x:Key="SecondaryButtonHoverColor">#EEEEEE</Color>
    <Color x:Key="SecondaryButtonPressedColor">#E0E0E0</Color>
    
    <!-- ألوان التحديد والتركيز -->
    <Color x:Key="SelectionColor">#E3F2FD</Color>
    <Color x:Key="HoverColor">#F5F5F5</Color>
    <Color x:Key="FocusColor">#2196F3</Color>
    <Color x:Key="ActiveColor">#1976D2</Color>
    <Color x:Key="HighlightColor">#FFEB3B</Color>
    
    <!-- ألوان الحالة -->
    <Color x:Key="SuccessColor">#4CAF50</Color>
    <Color x:Key="SuccessLightColor">#C8E6C9</Color>
    <Color x:Key="SuccessDarkColor">#2E7D32</Color>
    
    <Color x:Key="WarningColor">#FF9800</Color>
    <Color x:Key="WarningLightColor">#FFE0B2</Color>
    <Color x:Key="WarningDarkColor">#F57C00</Color>
    
    <Color x:Key="ErrorColor">#F44336</Color>
    <Color x:Key="ErrorLightColor">#FFCDD2</Color>
    <Color x:Key="ErrorDarkColor">#D32F2F</Color>
    
    <Color x:Key="InfoColor">#2196F3</Color>
    <Color x:Key="InfoLightColor">#BBDEFB</Color>
    <Color x:Key="InfoDarkColor">#1976D2</Color>
    
    <!-- ألوان الجداول -->
    <Color x:Key="TableHeaderColor">#F5F5F5</Color>
    <Color x:Key="TableRowEvenColor">#FAFAFA</Color>
    <Color x:Key="TableRowOddColor">#FFFFFF</Color>
    <Color x:Key="TableBorderColor">#E0E0E0</Color>
    <Color x:Key="TableSelectedRowColor">#E3F2FD</Color>
    <Color x:Key="TableHoverRowColor">#F5F5F5</Color>
    
    <!-- ألوان الرسوم البيانية -->
    <Color x:Key="Chart1Color">#2196F3</Color>
    <Color x:Key="Chart2Color">#4CAF50</Color>
    <Color x:Key="Chart3Color">#FF9800</Color>
    <Color x:Key="Chart4Color">#9C27B0</Color>
    <Color x:Key="Chart5Color">#F44336</Color>
    <Color x:Key="Chart6Color">#00BCD4</Color>
    <Color x:Key="Chart7Color">#8BC34A</Color>
    <Color x:Key="Chart8Color">#FFC107</Color>
    
    <!-- ألوان الأقسام -->
    <Color x:Key="SalesColor">#4CAF50</Color>
    <Color x:Key="PurchasesColor">#FF9800</Color>
    <Color x:Key="InventoryColor">#2196F3</Color>
    <Color x:Key="CustomersColor">#9C27B0</Color>
    <Color x:Key="SuppliersColor">#FF5722</Color>
    <Color x:Key="AccountingColor">#795548</Color>
    <Color x:Key="ReportsColor">#607D8B</Color>
    <Color x:Key="SettingsColor">#9E9E9E</Color>
    
    <!-- فرش الألوان (Brushes) -->
    <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="PrimaryDarkBrush" Color="{StaticResource PrimaryDarkColor}"/>
    <SolidColorBrush x:Key="PrimaryLightBrush" Color="{StaticResource PrimaryLightColor}"/>
    
    <SolidColorBrush x:Key="SecondaryBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="SecondaryDarkBrush" Color="{StaticResource SecondaryDarkColor}"/>
    <SolidColorBrush x:Key="SecondaryLightBrush" Color="{StaticResource SecondaryLightColor}"/>
    
    <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
    
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="{StaticResource SurfaceColor}"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="{StaticResource CardBackgroundColor}"/>
    <SolidColorBrush x:Key="DialogBackgroundBrush" Color="{StaticResource DialogBackgroundColor}"/>
    <SolidColorBrush x:Key="SidebarBackgroundBrush" Color="{StaticResource SidebarBackgroundColor}"/>
    <SolidColorBrush x:Key="HeaderBackgroundBrush" Color="{StaticResource HeaderBackgroundColor}"/>
    <SolidColorBrush x:Key="FooterBackgroundBrush" Color="{StaticResource FooterBackgroundColor}"/>
    
    <SolidColorBrush x:Key="PrimaryTextBrush" Color="{StaticResource PrimaryTextColor}"/>
    <SolidColorBrush x:Key="SecondaryTextBrush" Color="{StaticResource SecondaryTextColor}"/>
    <SolidColorBrush x:Key="DisabledTextBrush" Color="{StaticResource DisabledTextColor}"/>
    <SolidColorBrush x:Key="HintTextBrush" Color="{StaticResource HintTextColor}"/>
    <SolidColorBrush x:Key="OnPrimaryTextBrush" Color="{StaticResource OnPrimaryTextColor}"/>
    <SolidColorBrush x:Key="OnSecondaryTextBrush" Color="{StaticResource OnSecondaryTextColor}"/>
    <SolidColorBrush x:Key="OnSurfaceTextBrush" Color="{StaticResource OnSurfaceTextColor}"/>
    <SolidColorBrush x:Key="OnBackgroundTextBrush" Color="{StaticResource OnBackgroundTextColor}"/>
    
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>
    <SolidColorBrush x:Key="DividerBrush" Color="{StaticResource DividerColor}"/>
    <SolidColorBrush x:Key="OutlineBrush" Color="{StaticResource OutlineColor}"/>
    <SolidColorBrush x:Key="FocusBorderBrush" Color="{StaticResource FocusBorderColor}"/>
    <SolidColorBrush x:Key="ErrorBorderBrush" Color="{StaticResource ErrorBorderColor}"/>
    
    <SolidColorBrush x:Key="ButtonBackgroundBrush" Color="{StaticResource ButtonBackgroundColor}"/>
    <SolidColorBrush x:Key="ButtonTextBrush" Color="{StaticResource ButtonTextColor}"/>
    <SolidColorBrush x:Key="ButtonHoverBrush" Color="{StaticResource ButtonHoverColor}"/>
    <SolidColorBrush x:Key="ButtonPressedBrush" Color="{StaticResource ButtonPressedColor}"/>
    <SolidColorBrush x:Key="ButtonDisabledBrush" Color="{StaticResource ButtonDisabledColor}"/>
    <SolidColorBrush x:Key="ButtonDisabledTextBrush" Color="{StaticResource ButtonDisabledTextColor}"/>
    
    <SolidColorBrush x:Key="SecondaryButtonBackgroundBrush" Color="{StaticResource SecondaryButtonBackgroundColor}"/>
    <SolidColorBrush x:Key="SecondaryButtonTextBrush" Color="{StaticResource SecondaryButtonTextColor}"/>
    <SolidColorBrush x:Key="SecondaryButtonHoverBrush" Color="{StaticResource SecondaryButtonHoverColor}"/>
    <SolidColorBrush x:Key="SecondaryButtonPressedBrush" Color="{StaticResource SecondaryButtonPressedColor}"/>
    
    <SolidColorBrush x:Key="SelectionBrush" Color="{StaticResource SelectionColor}"/>
    <SolidColorBrush x:Key="HoverBrush" Color="{StaticResource HoverColor}"/>
    <SolidColorBrush x:Key="FocusBrush" Color="{StaticResource FocusColor}"/>
    <SolidColorBrush x:Key="ActiveBrush" Color="{StaticResource ActiveColor}"/>
    <SolidColorBrush x:Key="HighlightBrush" Color="{StaticResource HighlightColor}"/>
    
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="SuccessLightBrush" Color="{StaticResource SuccessLightColor}"/>
    <SolidColorBrush x:Key="SuccessDarkBrush" Color="{StaticResource SuccessDarkColor}"/>
    
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="WarningLightBrush" Color="{StaticResource WarningLightColor}"/>
    <SolidColorBrush x:Key="WarningDarkBrush" Color="{StaticResource WarningDarkColor}"/>
    
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="ErrorLightBrush" Color="{StaticResource ErrorLightColor}"/>
    <SolidColorBrush x:Key="ErrorDarkBrush" Color="{StaticResource ErrorDarkColor}"/>
    
    <SolidColorBrush x:Key="InfoBrush" Color="{StaticResource InfoColor}"/>
    <SolidColorBrush x:Key="InfoLightBrush" Color="{StaticResource InfoLightColor}"/>
    <SolidColorBrush x:Key="InfoDarkBrush" Color="{StaticResource InfoDarkColor}"/>
    
    <SolidColorBrush x:Key="TableHeaderBrush" Color="{StaticResource TableHeaderColor}"/>
    <SolidColorBrush x:Key="TableRowEvenBrush" Color="{StaticResource TableRowEvenColor}"/>
    <SolidColorBrush x:Key="TableRowOddBrush" Color="{StaticResource TableRowOddColor}"/>
    <SolidColorBrush x:Key="TableBorderBrush" Color="{StaticResource TableBorderColor}"/>
    <SolidColorBrush x:Key="TableSelectedRowBrush" Color="{StaticResource TableSelectedRowColor}"/>
    <SolidColorBrush x:Key="TableHoverRowBrush" Color="{StaticResource TableHoverRowColor}"/>
    
    <SolidColorBrush x:Key="Chart1Brush" Color="{StaticResource Chart1Color}"/>
    <SolidColorBrush x:Key="Chart2Brush" Color="{StaticResource Chart2Color}"/>
    <SolidColorBrush x:Key="Chart3Brush" Color="{StaticResource Chart3Color}"/>
    <SolidColorBrush x:Key="Chart4Brush" Color="{StaticResource Chart4Color}"/>
    <SolidColorBrush x:Key="Chart5Brush" Color="{StaticResource Chart5Color}"/>
    <SolidColorBrush x:Key="Chart6Brush" Color="{StaticResource Chart6Color}"/>
    <SolidColorBrush x:Key="Chart7Brush" Color="{StaticResource Chart7Color}"/>
    <SolidColorBrush x:Key="Chart8Brush" Color="{StaticResource Chart8Color}"/>
    
    <SolidColorBrush x:Key="SalesBrush" Color="{StaticResource SalesColor}"/>
    <SolidColorBrush x:Key="PurchasesBrush" Color="{StaticResource PurchasesColor}"/>
    <SolidColorBrush x:Key="InventoryBrush" Color="{StaticResource InventoryColor}"/>
    <SolidColorBrush x:Key="CustomersBrush" Color="{StaticResource CustomersColor}"/>
    <SolidColorBrush x:Key="SuppliersBrush" Color="{StaticResource SuppliersColor}"/>
    <SolidColorBrush x:Key="AccountingBrush" Color="{StaticResource AccountingColor}"/>
    <SolidColorBrush x:Key="ReportsBrush" Color="{StaticResource ReportsColor}"/>
    <SolidColorBrush x:Key="SettingsBrush" Color="{StaticResource SettingsColor}"/>

</ResourceDictionary>