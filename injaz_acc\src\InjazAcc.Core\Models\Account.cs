using System;
using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج الحساب المالي في النظام
    /// </summary>
    public class Account
    {
        public int Id { get; set; }
        public string? Code { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public AccountType Type { get; set; }
        public bool IsActive { get; set; }
        public decimal OpeningBalance { get; set; }
        public DateTime OpeningBalanceDate { get; set; }
        public decimal CurrentBalance { get; set; }

        // العلاقة مع الحساب الأب (للحسابات الفرعية)
        public int? ParentAccountId { get; set; }
        public virtual Account? ParentAccount { get; set; }

        // العلاقة مع الحسابات الفرعية
        public virtual ICollection<Account>? SubAccounts { get; set; }

        // العلاقة مع القيود المحاسبية
        public virtual ICollection<JournalEntryItem>? JournalEntryItems { get; set; }

        // العلاقة مع المدفوعات
        public virtual ICollection<Payment>? Payments { get; set; }

        public bool IsParent { get; set; }
    }

    // تم نقل تعريف أنواع الحسابات إلى ملف منفصل AccountType.cs
}
