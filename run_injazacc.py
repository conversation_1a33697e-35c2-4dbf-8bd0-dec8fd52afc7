import os
import subprocess
import sys
import time

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🎨 برنامج إنجاز المحاسبي - نظام التحكم الشامل بالألوان")
    print("=" * 60)
    print("✨ المميزات الجديدة:")
    print("   • التحكم في +80 لون مختلف")
    print("   • 8 قوالب ألوان جاهزة")
    print("   • منتقي ألوان متقدم")
    print("   • معاينة فورية للتغييرات")
    print("   • تصدير واستيراد الإعدادات")
    print("=" * 60)

def check_dotnet():
    """التحقق من وجود .NET"""
    try:
        result = subprocess.run(["dotnet", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"✅ تم العثور على .NET إصدار: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ لم يتم العثور على .NET")
        print("يرجى تثبيت .NET من: https://dotnet.microsoft.com/download")
        return False

def check_project_structure():
    """التحقق من بنية المشروع"""
    project_path = os.path.join('injaz_acc', 'src', 'InjazAcc.UI', 'InjazAcc.UI.csproj')
    
    if not os.path.exists('injaz_acc'):
        print("❌ مجلد المشروع غير موجود")
        return False, None
        
    if not os.path.exists(project_path):
        print("❌ ملف المشروع غير موجود")
        return False, None
        
    print("✅ تم العثور على ملف المشروع")
    return True, project_path

def restore_packages(project_path):
    """استعادة حزم NuGet"""
    print("📦 جاري استعادة حزم NuGet...")
    try:
        subprocess.run(["dotnet", "restore", project_path], 
                      check=True, capture_output=True)
        print("✅ تم استعادة الحزم بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في استعادة الحزم: {e}")
        return False

def build_project(project_path):
    """بناء المشروع"""
    print("🔨 جاري بناء المشروع...")
    try:
        result = subprocess.run(["dotnet", "build", project_path], 
                              check=True, capture_output=True, text=True)
        print("✅ تم بناء المشروع بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء المشروع:")
        if e.stderr:
            print(e.stderr)
        return False

def run_application(project_path):
    """تشغيل التطبيق"""
    print("🚀 جاري تشغيل البرنامج...")
    print("=" * 60)
    
    try:
        # تشغيل البرنامج مع إظهار المخرجات مباشرة
        process = subprocess.Popen(
            ["dotnet", "run", "--project", project_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # طباعة المخرجات في الوقت الفعلي
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        return_code = process.poll()
        if return_code == 0:
            print("✅ تم إغلاق البرنامج بنجاح")
        else:
            print(f"❌ البرنامج أغلق برمز خطأ: {return_code}")
            
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        process.terminate()
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من .NET
    if not check_dotnet():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من بنية المشروع
    project_exists, project_path = check_project_structure()
    if not project_exists:
        input("اضغط Enter للخروج...")
        return
    
    # استعادة الحزم
    if not restore_packages(project_path):
        input("اضغط Enter للخروج...")
        return
    
    # بناء المشروع
    if not build_project(project_path):
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل التطبيق
    run_application(project_path)
    
    print("=" * 60)
    print("شكراً لاستخدام برنامج إنجاز المحاسبي! 🙏")
    input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
