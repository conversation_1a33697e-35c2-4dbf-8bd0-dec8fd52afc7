using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using InjazAcc.UI.Helpers;

namespace InjazAcc.UI.Views.Settings
{
    /// <summary>
    /// صفحة اختبار الألوان الشامل
    /// </summary>
    public partial class ColorTestPage : Page
    {
        private ObservableCollection<TestDataItem> _testData = new ObservableCollection<TestDataItem>(); // تهيئة الحقل

        public ColorTestPage()
        {
            InitializeComponent();
            InitializeTestData();
            DrawCharts();
            CreateSidebarTest();
        }

        /// <summary>
        /// تهيئة البيانات التجريبية
        /// </summary>
        private void InitializeTestData()
        {
            try
            {
                _testData = new ObservableCollection<TestDataItem>
                {
                    new TestDataItem { Id = 1, Name = "منتج أ", Type = "مبيعات", Amount = 1500.00m, Date = "2024-01-15", Status = "مكتمل" },
                    new TestDataItem { Id = 2, Name = "منتج ب", Type = "مشتريات", Amount = 800.00m, Date = "2024-01-16", Status = "قيد المعالجة" },
                    new TestDataItem { Id = 3, Name = "منتج ج", Type = "مبيعات", Amount = 2200.00m, Date = "2024-01-17", Status = "مكتمل" },
                    new TestDataItem { Id = 4, Name = "منتج د", Type = "مشتريات", Amount = 950.00m, Date = "2024-01-18", Status = "ملغي" },
                    new TestDataItem { Id = 5, Name = "منتج هـ", Type = "مبيعات", Amount = 1750.00m, Date = "2024-01-19", Status = "مكتمل" },
                    new TestDataItem { Id = 6, Name = "منتج و", Type = "مخزون", Amount = 1200.00m, Date = "2024-01-20", Status = "قيد المراجعة" },
                    new TestDataItem { Id = 7, Name = "منتج ز", Type = "مبيعات", Amount = 3000.00m, Date = "2024-01-21", Status = "مكتمل" },
                    new TestDataItem { Id = 8, Name = "منتج ح", Type = "مشتريات", Amount = 650.00m, Date = "2024-01-22", Status = "قيد المعالجة" }
                };

                testDataGrid.ItemsSource = _testData;
                testDataGrid.AlternationCount = 2;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة البيانات التجريبية: {ex.Message}");
            }
        }

        /// <summary>
        /// رسم الرسوم البيانية
        /// </summary>
        private void DrawCharts()
        {
            try
            {
                DrawBarChart();
                DrawPieChart();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في رسم الرسوم البيانية: {ex.Message}");
            }
        }

        /// <summary>
        /// رسم الرسم البياني بالأعمدة
        /// </summary>
        private void DrawBarChart()
        {
            try
            {
                barChart?.Children.Clear();
                
                var colors = ChartColorManager.GetChartColors();
                var data = new double[] { 25, 35, 20, 15, 30, 40, 18, 22 };
                var labels = new string[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس" };

                double barWidth = 30;
                double spacing = 10;
                double maxHeight = 180;
                double maxValue = 40;
                double startX = 30;
                double startY = 200;

                // رسم المحاور
                var xAxis = new Line
                {
                    X1 = startX - 10,
                    Y1 = startY,
                    X2 = startX + (barWidth + spacing) * data.Length,
                    Y2 = startY,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString(AdvancedColorManager.CurrentSettings.BorderColor)),
                    StrokeThickness = 2
                };
                barChart.Children.Add(xAxis);

                var yAxis = new Line
                {
                    X1 = startX - 10,
                    Y1 = startY,
                    X2 = startX - 10,
                    Y2 = startY - maxHeight - 20,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString(AdvancedColorManager.CurrentSettings.BorderColor)),
                    StrokeThickness = 2
                };
                barChart.Children.Add(yAxis);

                // رسم الأعمدة
                for (int i = 0; i < data.Length; i++)
                {
                    var barHeight = (data[i] / maxValue) * maxHeight;
                    var rect = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = new SolidColorBrush(colors[i % colors.Count])
                    };

                    Canvas.SetLeft(rect, startX + i * (barWidth + spacing));
                    Canvas.SetTop(rect, startY - barHeight);
                    barChart.Children.Add(rect);

                    // إضافة التسمية
                    var label = new TextBlock
                    {
                        Text = labels[i],
                        FontSize = 10,
                        Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString(AdvancedColorManager.CurrentSettings.PrimaryTextColor)),
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    Canvas.SetLeft(label, startX + i * (barWidth + spacing) + barWidth / 2 - 15);
                    Canvas.SetTop(label, startY + 5);
                    barChart.Children.Add(label);

                    // إضافة القيمة
                    var value = new TextBlock
                    {
                        Text = data[i].ToString(),
                        FontSize = 9,
                        Foreground = Brushes.White,
                        FontWeight = FontWeights.Bold,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    Canvas.SetLeft(value, startX + i * (barWidth + spacing) + barWidth / 2 - 8);
                    Canvas.SetTop(value, startY - barHeight / 2 - 6);
                    barChart.Children.Add(value);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في رسم الرسم البياني بالأعمدة: {ex.Message}");
            }
        }

        /// <summary>
        /// رسم الرسم البياني الدائري
        /// </summary>
        private void DrawPieChart()
        {
            try
            {
                pieChart?.Children.Clear();
                
                var colors = ChartColorManager.GetChartColors();
                var data = new double[] { 30, 25, 20, 15, 10 };
                var labels = new string[] { "المبيعات", "المشتريات", "المخزون", "العملاء", "أخرى" };
                
                double centerX = 125;
                double centerY = 125;
                double radius = 100;
                double total = 0;
                
                foreach (var value in data)
                    total += value;

                double currentAngle = 0;
                
                for (int i = 0; i < data.Length; i++)
                {
                    double sweepAngle = (data[i] / total) * 360;
                    
                    // إنشاء قطعة الدائرة
                    var pathGeometry = new PathGeometry();
                    var pathFigure = new PathFigure();
                    pathFigure.StartPoint = new Point(centerX, centerY);
                    
                    var startAngleRad = currentAngle * Math.PI / 180;
                    var endAngleRad = (currentAngle + sweepAngle) * Math.PI / 180;
                    
                    var startPoint = new Point(
                        centerX + radius * Math.Cos(startAngleRad),
                        centerY + radius * Math.Sin(startAngleRad));
                    
                    var endPoint = new Point(
                        centerX + radius * Math.Cos(endAngleRad),
                        centerY + radius * Math.Sin(endAngleRad));
                    
                    pathFigure.Segments.Add(new LineSegment(startPoint, true));
                    pathFigure.Segments.Add(new ArcSegment(endPoint, new Size(radius, radius), 0, sweepAngle > 180, SweepDirection.Clockwise, true));
                    pathFigure.Segments.Add(new LineSegment(new Point(centerX, centerY), true));
                    
                    pathGeometry.Figures.Add(pathFigure);
                    
                    var path = new Path
                    {
                        Data = pathGeometry,
                        Fill = new SolidColorBrush(colors[i % colors.Count]),
                        Stroke = Brushes.White,
                        StrokeThickness = 2
                    };
                    
                    pieChart.Children.Add(path);
                    
                    // إضافة التسمية
                    var labelAngle = currentAngle + sweepAngle / 2;
                    var labelAngleRad = labelAngle * Math.PI / 180;
                    var labelRadius = radius * 0.7;
                    
                    var labelX = centerX + labelRadius * Math.Cos(labelAngleRad);
                    var labelY = centerY + labelRadius * Math.Sin(labelAngleRad);
                    
                    var label = new TextBlock
                    {
                        Text = $"{labels[i]}\n{data[i]:F0}%",
                        FontSize = 10,
                        Foreground = Brushes.White,
                        FontWeight = FontWeights.Bold,
                        TextAlignment = TextAlignment.Center,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };
                    
                    Canvas.SetLeft(label, labelX - 25);
                    Canvas.SetTop(label, labelY - 15);
                    pieChart.Children.Add(label);
                    
                    currentAngle += sweepAngle;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في رسم الرسم البياني الدائري: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء اختبار القائمة الجانبية
        /// </summary>
        private void CreateSidebarTest()
        {
            try
            {
                sidebarTest?.Children.Clear();
                
                var sections = new[]
                {
                    new { Name = "المبيعات", Icon = "💰", Section = SectionColorManager.SectionType.Sales },
                    new { Name = "المشتريات", Icon = "🛒", Section = SectionColorManager.SectionType.Purchases },
                    new { Name = "المخزون", Icon = "📦", Section = SectionColorManager.SectionType.Inventory },
                    new { Name = "العملاء", Icon = "👥", Section = SectionColorManager.SectionType.Customers },
                    new { Name = "الموردين", Icon = "🏭", Section = SectionColorManager.SectionType.Suppliers },
                    new { Name = "المحاسبة", Icon = "📊", Section = SectionColorManager.SectionType.Accounting },
                    new { Name = "التقارير", Icon = "📈", Section = SectionColorManager.SectionType.Reports },
                    new { Name = "الإعدادات", Icon = "⚙️", Section = SectionColorManager.SectionType.Settings }
                };

                foreach (var section in sections)
                {
                    var button = new Button
                    {
                        Height = 50,
                        Margin = new Thickness(5),
                        HorizontalAlignment = HorizontalAlignment.Stretch,
                        HorizontalContentAlignment = HorizontalAlignment.Right,
                        Padding = new Thickness(15, 10, 15, 10) // إضافة القيم المفقودة
                    };

                    var stackPanel = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        HorizontalAlignment = HorizontalAlignment.Right
                    };

                    var icon = new TextBlock
                    {
                        Text = section.Icon,
                        FontSize = 20,
                        Margin = new Thickness(0, 0, 10, 0),
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    var text = new TextBlock
                    {
                        Text = section.Name,
                        FontSize = 14,
                        FontWeight = FontWeights.Bold,
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    stackPanel.Children.Add(text);
                    stackPanel.Children.Add(icon);
                    button.Content = stackPanel;

                    // تطبيق لون القسم
                    SectionColorManager.ApplySectionColorToElement(button, section.Section);

                    sidebarTest.Children.Add(button);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء اختبار القائمة الجانبية: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الألوان عند تغييرها
        /// </summary>
        public void RefreshColors()
        {
            try
            {
                DrawCharts();
                CreateSidebarTest();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الألوان: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// عنصر بيانات الاختبار
    /// </summary>
    public class TestDataItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Date { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
    }
}