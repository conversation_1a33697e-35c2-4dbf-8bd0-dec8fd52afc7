import os
import sqlite3
import json
from datetime import datetime

def create_test_settings():
    """إنشاء إعدادات تجريبية للاختبار"""
    print("🧪 جاري إنشاء إعدادات تجريبية...")
    
    app_data = os.path.expanduser("~\\AppData\\Roaming\\InjazAcc")
    if not os.path.exists(app_data):
        os.makedirs(app_data)
        print(f"✅ تم إنشاء مجلد: {app_data}")
    
    # إنشاء قاعدة بيانات تجريبية
    settings_db = os.path.join(app_data, "settings.db")
    conn = sqlite3.connect(settings_db)
    cursor = conn.cursor()
    
    # إنشاء الجدول
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS SystemSettings (
            Id INTEGER PRIMARY KEY,
            SettingKey TEXT NOT NULL UNIQUE,
            SettingValue TEXT,
            Description TEXT,
            [Group] INTEGER,
            IsReadOnly INTEGER DEFAULT 0,
            CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
            UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إضافة إعدادات تجريبية
    test_settings = [
        (1, "TestSetting1", "Value1", "إعداد تجريبي 1", 0, 0),
        (2, "TestSetting2", "Value2", "إعداد تجريبي 2", 0, 0),
        (3, "LastAppCloseTime", datetime.now().isoformat(), "آخر إغلاق للتطبيق", 1, 0),
        (4, "AppClosedProperly", "true", "إغلاق صحيح للتطبيق", 1, 0),
        (5, "AutoBackup", "true", "النسخ الاحتياطي التلقائي", 0, 0),
        (6, "ShowWelcomeScreen", "true", "عرض شاشة الترحيب", 0, 0),
        (7, "DefaultTaxRate", "15", "معدل الضريبة الافتراضي", 0, 0),
        (8, "SalesInvoicePrefix", "INV", "بادئة فاتورة المبيعات", 0, 0),
    ]
    
    for setting in test_settings:
        cursor.execute('''
            INSERT OR REPLACE INTO SystemSettings 
            (Id, SettingKey, SettingValue, Description, [Group], IsReadOnly)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', setting)
    
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء قاعدة بيانات الإعدادات التجريبية")
    
    # إنشاء ملف إعدادات الألوان
    theme_settings = {
        "PrimaryColor": "Teal",
        "SecondaryColor": "Amber", 
        "BaseTheme": "Light",
        "BackgroundColor": "#FFFFFF",
        "TextColor": "#000000",
        "BorderColor": "#CCCCCC",
        "ButtonColor": "#009688",
        "SelectionColor": "#E3F2FD",
        "WarningColor": "#FF9800"
    }
    
    theme_file = os.path.join(app_data, "theme_settings.json")
    with open(theme_file, 'w', encoding='utf-8') as f:
        json.dump(theme_settings, f, indent=2, ensure_ascii=False)
    
    print("✅ تم إنشاء ملف إعدادات الألوان")
    
    # إنشاء ملف إعدادات الألوان المتقدمة
    advanced_settings = {
        "CustomColors": {
            "PrimaryColor": "#009688",
            "SecondaryColor": "#FFC107",
            "BackgroundColor": "#FFFFFF",
            "TextColor": "#000000"
        },
        "ColorTemplates": [
            {
                "Name": "الافتراضي",
                "Colors": {
                    "Primary": "#009688",
                    "Secondary": "#FFC107"
                }
            }
        ]
    }
    
    advanced_file = os.path.join(app_data, "advanced_color_settings.json")
    with open(advanced_file, 'w', encoding='utf-8') as f:
        json.dump(advanced_settings, f, indent=2, ensure_ascii=False)
    
    print("✅ تم إنشاء ملف إعدادات الألوان المتقدمة")
    
    # إنشاء نسخة احتياطية JSON
    backup_settings = [
        {
            "Id": 1,
            "SettingKey": "TestSetting1",
            "SettingValue": "Value1",
            "Description": "إعداد تجريبي 1",
            "Group": 0,
            "IsReadOnly": False
        }
    ]
    
    backup_file = os.path.join(app_data, "settings_backup.json")
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(backup_settings, f, indent=2, ensure_ascii=False)
    
    print("✅ تم إنشاء النسخة الاحتياطية JSON")
    print("🎉 تم إنشاء جميع الإعدادات التجريبية بنجاح!")
    
    # عرض ملخص
    print("\n📊 ملخص الإعدادات المنشأة:")
    print(f"   📁 المجلد: {app_data}")
    print(f"   💾 قاعدة البيانات: {len(test_settings)} إعدادات")
    print(f"   🎨 إعدادات الألوان: {len(theme_settings)} خاصية")
    print(f"   🌈 إعدادات الألوان المتقدمة: {len(advanced_settings)} مجموعة")

if __name__ == "__main__":
    create_test_settings()
    input("\nاضغط Enter للخروج...")