# إعداد قاعدة البيانات - InjazAcc

## ✅ تم إنشاء قاعدة البيانات بنجاح!

### المستخدم الافتراضي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الدور**: مدير النظام

### سلسلة الاتصال
```
Server=(localdb)\mssqllocaldb;Database=InjazAccDB;Trusted_Connection=true;MultipleActiveResultSets=true
```

### تشغيل التطبيق
```bash
python run_injazacc.py
```

عند تشغيل التطبيق لأول مرة، سيتم تهيئة قاعدة البيانات تلقائياً مع جميع البيانات الأولية.

## البيانات الأولية المضافة

### المستخدم الافتراضي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الدور**: مدير النظام

### الأدوار الأساسية
- مدير النظام
- مدير مالي
- مدير مبيعات
- مدير مشتريات
- مدير مخزون
- محاسب
- كاشير
- مستخدم

### الحسابات المحاسبية
- الأصول (1)
  - الأصول المتداولة (11)
    - النقدية والبنوك (111)
    - العملاء (112)
    - المخزون (113)
    - المصروفات المدفوعة مقدماً (114)

- الخصوم (2)
  - الخصوم المتداولة (21)
    - الموردين (211)
    - المصروفات المستحقة (212)
    - ضريبة القيمة المضافة (213)

- حقوق الملكية (3)
  - رأس المال (31)
  - الأرباح المحتجزة (32)

- الإيرادات (4)
  - إيرادات المبيعات (41)
  - إيرادات أخرى (42)

- المصروفات (5)
  - تكلفة البضاعة المباعة (51)
  - مصروفات التشغيل (52)
  - مصروفات إدارية (53)
  - مصروفات تسويقية (54)

### الوحدات الأساسية
- قطعة، كيلوجرام، جرام، لتر، متر، متر مربع، متر مكعب، صندوق، كرتون، طن

### الفئات الأساسية
- مواد غذائية، مواد تنظيف، أدوات مكتبية، إلكترونيات، ملابس، أدوات منزلية، مواد بناء، قطع غيار، أدوية، متنوعة

### المخازن الأساسية
- المخزن الرئيسي (MAIN)
- مخزن الفرع الأول (BR01)
- مخزن المردودات (RET)

### معلومات الشركة الافتراضية
- الاسم: شركة إنجاز للتجارة
- الاسم القانوني: شركة إنجاز للتجارة المحدودة
- الرقم الضريبي: 123456789
- السجل التجاري: 1010123456

### السنة المالية
- السنة المالية الحالية: من 1 يناير إلى 31 ديسمبر

### إعدادات النظام
- اللغة الافتراضية: العربية
- تنسيق التاريخ: dd/MM/yyyy
- عدد الخانات العشرية: 2
- معدل ضريبة القيمة المضافة: 15%
- المظهر: فاتح
- اللون الأساسي: Teal

## سلسلة الاتصال
```
Server=(localdb)\mssqllocaldb;Database=InjazAccDB;Trusted_Connection=true;MultipleActiveResultSets=true
```

## الملفات المهمة
- `InjazAccDbContext.cs` - سياق قاعدة البيانات
- `DatabaseInitializer.cs` - تهيئة البيانات الأولية
- `DatabaseService.cs` - خدمات إدارة قاعدة البيانات
- `appsettings.json` - إعدادات التطبيق

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تثبيت SQL Server LocalDB
2. تحقق من سلسلة الاتصال في appsettings.json
3. تأكد من وجود صلاحيات الكتابة

### خطأ في Migration
```bash
dotnet ef migrations remove --startup-project ../InjazAcc.UI
dotnet ef migrations add InitialCreate --startup-project ../InjazAcc.UI
```

### إعادة تعيين قاعدة البيانات
يمكن إعادة تعيين قاعدة البيانات من خلال:
- قائمة الإعدادات في التطبيق
- أو حذف ملف قاعدة البيانات يدوياً

## الصيانة
- يتم إنشاء نسخ احتياطية تلقائياً (اختياري)
- يمكن تصدير البيانات إلى Excel
- سجلات الأخطاء في مجلد Logs

## الدعم
للمساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة ملفات السجل في مجلد Logs.
