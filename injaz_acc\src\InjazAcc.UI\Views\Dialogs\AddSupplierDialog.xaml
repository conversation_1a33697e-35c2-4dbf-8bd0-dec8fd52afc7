<Window x:Class="InjazAcc.UI.Views.Dialogs.AddSupplierDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة مورد جديد" Height="550" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إضافة مورد جديد" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- كود المورد -->
                <TextBox x:Name="txtSupplierCode" 
                         materialDesign:HintAssist.Hint="كود المورد"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- اسم المورد -->
                <TextBox x:Name="txtSupplierName" 
                         materialDesign:HintAssist.Hint="اسم المورد *"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- الشخص المسؤول -->
                <TextBox x:Name="txtContactPerson" 
                         materialDesign:HintAssist.Hint="الشخص المسؤول"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- رقم الهاتف -->
                <TextBox x:Name="txtPhone" 
                         materialDesign:HintAssist.Hint="رقم الهاتف"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- البريد الإلكتروني -->
                <TextBox x:Name="txtEmail" 
                         materialDesign:HintAssist.Hint="البريد الإلكتروني"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- العنوان -->
                <TextBox x:Name="txtAddress" 
                         materialDesign:HintAssist.Hint="العنوان"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="2"
                         MaxLines="4"
                         Margin="0,10"/>

                <!-- الرقم الضريبي -->
                <TextBox x:Name="txtTaxNumber" 
                         materialDesign:HintAssist.Hint="الرقم الضريبي"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- حد الائتمان -->
                <TextBox x:Name="txtCreditLimit" 
                         materialDesign:HintAssist.Hint="حد الائتمان"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- الرصيد الافتتاحي -->
                <TextBox x:Name="txtOpeningBalance" 
                         materialDesign:HintAssist.Hint="الرصيد الافتتاحي"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- تاريخ الرصيد الافتتاحي -->
                <DatePicker x:Name="dpOpeningBalanceDate" 
                            materialDesign:HintAssist.Hint="تاريخ الرصيد الافتتاحي"
                            Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                            Margin="0,10"/>

                <!-- ملاحظات -->
                <TextBox x:Name="txtNotes" 
                         materialDesign:HintAssist.Hint="ملاحظات"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="3"
                         MaxLines="5"
                         Margin="0,10"/>

                <!-- نشط -->
                <CheckBox x:Name="chkIsActive" 
                          Content="نشط" 
                          IsChecked="True"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Margin="0,15"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="btnSave" Content="حفظ" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Foreground="White"
                    Width="100" Margin="10,0"
                    Click="btnSave_Click"/>
            
            <Button x:Name="btnCancel" Content="إلغاء" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="100" Margin="10,0"
                    Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
