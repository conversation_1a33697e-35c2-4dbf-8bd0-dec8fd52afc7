﻿#pragma checksum "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "19E5CF9111E875F2E3971E545347595831B87D59"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Shared {
    
    
    /// <summary>
    /// AdvancedColorPickerWindow
    /// </summary>
    public partial class AdvancedColorPickerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas colorWheel;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas brightnessBar;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas alphaBar;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border currentColorPreview;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border originalColorPreview;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtRed;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtGreen;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtBlue;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHue;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSaturation;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtValue;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtHexColor;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel savedColorsPanel;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel quickColorsPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/shared/advancedcolorpickerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.colorWheel = ((System.Windows.Controls.Canvas)(target));
            
            #line 42 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.colorWheel.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorWheel_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 42 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.colorWheel.MouseMove += new System.Windows.Input.MouseEventHandler(this.ColorWheel_MouseMove);
            
            #line default
            #line hidden
            return;
            case 2:
            this.brightnessBar = ((System.Windows.Controls.Canvas)(target));
            
            #line 50 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.brightnessBar.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.BrightnessBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 50 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.brightnessBar.MouseMove += new System.Windows.Input.MouseEventHandler(this.BrightnessBar_MouseMove);
            
            #line default
            #line hidden
            return;
            case 3:
            this.alphaBar = ((System.Windows.Controls.Canvas)(target));
            
            #line 59 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.alphaBar.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AlphaBar_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 59 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.alphaBar.MouseMove += new System.Windows.Input.MouseEventHandler(this.AlphaBar_MouseMove);
            
            #line default
            #line hidden
            return;
            case 4:
            this.currentColorPreview = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.originalColorPreview = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.txtRed = ((System.Windows.Controls.TextBox)(target));
            
            #line 108 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.txtRed.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.RgbTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.txtGreen = ((System.Windows.Controls.TextBox)(target));
            
            #line 111 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.txtGreen.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.RgbTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.txtBlue = ((System.Windows.Controls.TextBox)(target));
            
            #line 114 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.txtBlue.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.RgbTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.txtHue = ((System.Windows.Controls.TextBox)(target));
            
            #line 132 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.txtHue.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.HsvTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.txtSaturation = ((System.Windows.Controls.TextBox)(target));
            
            #line 135 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.txtSaturation.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.HsvTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.txtValue = ((System.Windows.Controls.TextBox)(target));
            
            #line 138 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.txtValue.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.HsvTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.txtHexColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 144 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            this.txtHexColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.HexTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.savedColorsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 14:
            this.quickColorsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 15:
            
            #line 168 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveColor_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 170 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OK_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 172 "..\..\..\..\..\Views\Shared\AdvancedColorPickerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

