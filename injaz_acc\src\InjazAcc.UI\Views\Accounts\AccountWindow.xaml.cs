using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.DataAccess;
using InjazAcc.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for AccountWindow.xaml
    /// </summary>
    public partial class AccountWindow : Window
    {
        private bool _isEditMode = false;
        private AccountItem? _originalAccount;
        private readonly InjazAccDbContext _dbContext;
        
        public string AccountNumber { get; set; } = string.Empty;
        public string AccountName { get; set; } = string.Empty;
        public string AccountType { get; set; } = string.Empty;
        public int Level { get; set; }
        public string ParentAccount { get; set; } = string.Empty;
        public string Description { get; private set; } = string.Empty;

        // قائمة الحسابات الرئيسية
        private Dictionary<string, List<string>> _parentAccounts = new Dictionary<string, List<string>>
        {
            { "الأصول", new List<string> { "1000 - الأصول" } },
            { "الخصوم", new List<string> { "2000 - الخصوم" } },
            { "حقوق الملكية", new List<string> { "3000 - حقوق الملكية" } },
            { "الإيرادات", new List<string> { "4000 - الإيرادات" } },
            { "المصروفات", new List<string> { "5000 - المصروفات" } }
        };
        
        // قائمة الحسابات الفرعية للمستوى الثالث
        private Dictionary<string, List<string>> _level2Accounts = new Dictionary<string, List<string>>
        {
            { "الأصول", new List<string> { "1010 - الصندوق", "1020 - البنك", "1030 - المخزون", "1040 - العملاء", "1050 - أثاث ومعدات" } },
            { "الخصوم", new List<string> { "2010 - الموردين", "2020 - قروض قصيرة الأجل", "2030 - مصروفات مستحقة" } },
            { "حقوق الملكية", new List<string> { "3010 - رأس المال", "3020 - الأرباح المحتجزة" } },
            { "الإيرادات", new List<string> { "4010 - المبيعات", "4020 - إيرادات أخرى" } },
            { "المصروفات", new List<string> { "5010 - تكلفة المبيعات", "5020 - رواتب وأجور", "5030 - إيجارات", "5040 - مصروفات عمومية وإدارية" } }
        };

        // وضع الإضافة
        public AccountWindow(InjazAccDbContext dbContext)
        {
            InitializeComponent();
            _dbContext = dbContext;
            _isEditMode = false;
            txtWindowTitle.Text = "إضافة حساب جديد";
            
            cmbAccountType.SelectedIndex = 0;
            cmbLevel.SelectedIndex = 0;
            
            UpdateParentAccounts();
        }

        // وضع التعديل
        public AccountWindow(AccountItem account, InjazAccDbContext dbContext)
        {
            InitializeComponent();
            _dbContext = dbContext;
            _isEditMode = true;
            _originalAccount = account;
            txtWindowTitle.Text = "تعديل حساب";
            
            // تعبئة البيانات
            txtAccountNumber.Text = account.AccountNumber;
            txtAccountName.Text = account.AccountName;
            
            // تحديد نوع الحساب
            for (int i = 0; i < cmbAccountType.Items.Count; i++)
            {
                if ((cmbAccountType.Items[i] as ComboBoxItem)?.Content.ToString() == account.AccountType)
                {
                    cmbAccountType.SelectedIndex = i;
                    break;
                }
            }
            
            // تحديد المستوى
            for (int i = 0; i < cmbLevel.Items.Count; i++)
            {
                if ((cmbLevel.Items[i] as ComboBoxItem)?.Content.ToString() == account.Level.ToString())
                {
                    cmbLevel.SelectedIndex = i;
                    break;
                }
            }
            
            UpdateParentAccounts();
            
            // تحديد الحساب الرئيسي
            for (int i = 0; i < cmbParentAccount.Items.Count; i++)
            {
                if (cmbParentAccount.Items[i].ToString() == account.ParentAccount)
                {
                    cmbParentAccount.SelectedIndex = i;
                    break;
                }
            }
        }

        // إضافة منشئ افتراضي بدون معلمات
        public AccountWindow() : this(new InjazAccDbContext(new DbContextOptionsBuilder<InjazAccDbContext>().UseInMemoryDatabase("DefaultDb").Options))
        {
            // يمكن تخصيص هذا المنشئ إذا لزم الأمر
        }

        private void UpdateParentAccounts()
        {
            try
            {
                cmbParentAccount.Items.Clear();
                
                string accountType = (cmbAccountType.SelectedItem as ComboBoxItem)?.Content.ToString();
                int level = int.Parse((cmbLevel.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "1");
                
                if (level == 1)
                {
                    // المستوى الأول ليس له حساب رئيسي
                    cmbParentAccount.IsEnabled = false;
                }
                else if (level == 2)
                {
                    // المستوى الثاني يكون الحساب الرئيسي له هو حساب المستوى الأول
                    cmbParentAccount.IsEnabled = true;
                    
                    if (!string.IsNullOrEmpty(accountType) && _parentAccounts.ContainsKey(accountType))
                    {
                        foreach (var parent in _parentAccounts[accountType])
                        {
                            cmbParentAccount.Items.Add(parent);
                        }
                    }
                }
                else if (level == 3)
                {
                    // المستوى الثالث يكون الحساب الرئيسي له هو حساب المستوى الثاني
                    cmbParentAccount.IsEnabled = true;
                    
                    if (!string.IsNullOrEmpty(accountType) && _level2Accounts.ContainsKey(accountType))
                    {
                        foreach (var parent in _level2Accounts[accountType])
                        {
                            cmbParentAccount.Items.Add(parent);
                        }
                    }
                }
                
                if (cmbParentAccount.Items.Count > 0)
                {
                    cmbParentAccount.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث قائمة الحسابات الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void cmbLevel_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateParentAccounts();
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtAccountNumber.Text))
                {
                    MessageBox.Show("الرجاء إدخال رقم الحساب", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAccountNumber.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtAccountName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم الحساب", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtAccountName.Focus();
                    return;
                }

                if (cmbAccountType.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار نوع الحساب", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbAccountType.Focus();
                    return;
                }

                if (cmbLevel.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار مستوى الحساب", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbLevel.Focus();
                    return;
                }

                int level = int.Parse((cmbLevel.SelectedItem as ComboBoxItem)?.Content.ToString() ?? "1");
                if (level > 1 && cmbParentAccount.SelectedItem == null)
                {
                    MessageBox.Show("الرجاء اختيار الحساب الرئيسي", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    cmbParentAccount.Focus();
                    return;
                }

                if (_isEditMode)
                {
                    // تحديث الحساب الحالي
                    var account = _dbContext.Accounts.FirstOrDefault(a => a.Id == _originalAccount.Id);
                    if (account != null)
                    {
                        account.Code = txtAccountNumber.Text;
                        account.Name = txtAccountName.Text;
                        account.Description = txtDescription.Text;
                        // Parse AccountType from ComboBox to enum
                        var typeStr = (cmbAccountType.SelectedItem as ComboBoxItem)?.Content.ToString();
                        if (!string.IsNullOrEmpty(typeStr))
                        {
                            if (Enum.TryParse<AccountType>(typeStr, out var accType))
                                account.Type = accType;
                        }
                        // ParentAccountId is not set here (requires lookup if needed)
                        // No Level or ParentAccount property in Account model
                    }
                }
                else
                {
                    // إضافة حساب جديد
                    var newAccount = new Account
                    {
                        Code = txtAccountNumber.Text,
                        Name = txtAccountName.Text,
                        Description = txtDescription.Text,
                        Type = (AccountType)Enum.Parse(typeof(AccountType), (cmbAccountType.SelectedItem as ComboBoxItem)?.Content.ToString() ?? ""),
                        ParentAccountId = level > 1 ? (int?)cmbParentAccount.SelectedItem : null
                    };
                    _dbContext.Accounts.Add(newAccount);
                }

                _dbContext.SaveChanges();

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
