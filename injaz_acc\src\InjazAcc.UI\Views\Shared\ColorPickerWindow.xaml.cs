using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace InjazAcc.UI.Views.Shared
{
    /// <summary>
    /// نافذة اختيار الألوان
    /// </summary>
    public partial class ColorPickerWindow : Window
    {
        public Color SelectedColor { get; set; } = Colors.White;

        private WrapPanel? colorPanel;
        private Border? selectedColorPreview;
        private TextBlock? previewText;

        public ColorPickerWindow()
        {
            InitializeComponent();
            InitializeElements();
            LoadColors();
        }

        /// <summary>
        /// تهيئة العناصر
        /// </summary>
        private void InitializeElements()
        {
            colorPanel = this.FindName("colorPanelContainer") as WrapPanel;
            selectedColorPreview = this.FindName("colorPreviewContainer") as Border;
            previewText = this.FindName("colorPreviewText") as TextBlock;
        }

        public ColorPickerWindow(Color initialColor) : this()
        {
            SelectedColor = initialColor;
            UpdatePreview(initialColor);
        }

        /// <summary>
        /// تحميل الألوان المتاحة
        /// </summary>
        private void LoadColors()
        {
            var colors = new[]
            {
                // الألوان الأساسية
                Colors.White, Colors.Black, Colors.Gray, Colors.LightGray, Colors.DarkGray,
                
                // الألوان الحمراء
                Colors.Red, Colors.DarkRed, Colors.Crimson, Colors.IndianRed, Colors.LightCoral,
                Colors.Salmon, Colors.DarkSalmon, Colors.LightSalmon, Colors.OrangeRed, Colors.Tomato,
                
                // الألوان البرتقالية
                Colors.Orange, Colors.DarkOrange, Colors.Coral, Colors.LightSalmon, Colors.PeachPuff,
                Colors.NavajoWhite, Colors.Moccasin, Colors.Bisque, Colors.MistyRose, Colors.BlanchedAlmond,
                
                // الألوان الصفراء
                Colors.Yellow, Colors.Gold, Colors.LightYellow, Colors.LemonChiffon, Colors.LightGoldenrodYellow,
                Colors.PapayaWhip, Colors.Cornsilk, Colors.Ivory, Colors.Beige, Colors.Khaki,
                
                // الألوان الخضراء
                Colors.Green, Colors.DarkGreen, Colors.ForestGreen, Colors.LimeGreen, Colors.Lime,
                Colors.LawnGreen, Colors.Chartreuse, Colors.GreenYellow, Colors.SpringGreen, Colors.MediumSpringGreen,
                Colors.LightGreen, Colors.PaleGreen, Colors.DarkSeaGreen, Colors.MediumSeaGreen, Colors.SeaGreen,
                
                // الألوان الزرقاء
                Colors.Blue, Colors.DarkBlue, Colors.Navy, Colors.MidnightBlue, Colors.CornflowerBlue,
                Colors.RoyalBlue, Colors.SteelBlue, Colors.LightSteelBlue, Colors.LightBlue, Colors.PowderBlue,
                Colors.LightSkyBlue, Colors.SkyBlue, Colors.DeepSkyBlue, Colors.DodgerBlue, Colors.Azure,
                
                // الألوان البنفسجية
                Colors.Purple, Colors.DarkMagenta, Colors.Magenta, Colors.Violet, Colors.Plum,
                Colors.Thistle, Colors.Lavender, Colors.MediumOrchid, Colors.MediumPurple, Colors.BlueViolet,
                Colors.DarkViolet, Colors.DarkOrchid, Colors.DarkSlateBlue, Colors.SlateBlue, Colors.MediumSlateBlue,
                
                // الألوان الوردية
                Colors.Pink, Colors.LightPink, Colors.HotPink, Colors.DeepPink, Colors.MediumVioletRed,
                Colors.PaleVioletRed, Colors.LavenderBlush, Colors.MistyRose, Colors.AntiqueWhite, Colors.Linen,
                
                // الألوان البنية
                Colors.Brown, Colors.SaddleBrown, Colors.Sienna, Colors.Chocolate, Colors.DarkGoldenrod,
                Colors.Peru, Colors.RosyBrown, Colors.Goldenrod, Colors.SandyBrown, Colors.Tan,
                Colors.BurlyWood, Colors.Wheat, Colors.NavajoWhite, Colors.Bisque, Colors.BlanchedAlmond,
                
                // ألوان أخرى
                Colors.Teal, Colors.DarkCyan, Colors.Cyan, Colors.LightCyan, Colors.PaleTurquoise,
                Colors.Aquamarine, Colors.Turquoise, Colors.MediumTurquoise, Colors.DarkTurquoise, Colors.CadetBlue,
                Colors.LightSeaGreen, Colors.DarkSlateGray, Colors.SlateGray, Colors.LightSlateGray, Colors.Silver
            };

            foreach (var color in colors)
            {
                var colorButton = new Border
                {
                    Width = 30,
                    Height = 30,
                    Margin = new Thickness(2),
                    Background = new SolidColorBrush(color),
                    BorderBrush = Brushes.Gray,
                    BorderThickness = new Thickness(1),
                    CornerRadius = new CornerRadius(3),
                    Cursor = System.Windows.Input.Cursors.Hand,
                    ToolTip = color.ToString()
                };

                colorButton.MouseLeftButtonDown += (s, e) =>
                {
                    SelectedColor = color;
                    UpdatePreview(color);
                };

                if (colorPanel != null) colorPanel.Children.Add(colorButton);
            }
        }

        /// <summary>
        /// تحديث معاينة اللون
        /// </summary>
        private void UpdatePreview(Color color)
        {
            if (selectedColorPreview != null) selectedColorPreview.Background = new SolidColorBrush(color);
            
            // تحديد لون النص بناءً على سطوع اللون
            var brightness = (color.R * 0.299 + color.G * 0.587 + color.B * 0.114) / 255;
            if (previewText != null) previewText.Foreground = brightness > 0.5 ? Brushes.Black : Brushes.White;
            if (previewText != null) previewText.Text = $"#{color.R:X2}{color.G:X2}{color.B:X2}";
        }

        /// <summary>
        /// تحديث معاينة اللون
        /// </summary>
        private void UpdateColorPreview()
        {
            if (selectedColorPreview != null) selectedColorPreview.Background = new SolidColorBrush(SelectedColor);
        }

        /// <summary>
        /// تحديث قيم اللون
        /// </summary>
        private void UpdateColorValues()
        {
            if (previewText != null) previewText.Text = $"#{SelectedColor.R:X2}{SelectedColor.G:X2}{SelectedColor.B:X2}";
        }

        /// <summary>
        /// زر موافق
        /// </summary>
        private void btnOK_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        /// <summary>
        /// زر إلغاء
        /// </summary>
        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        /// <summary>
        /// فتح منتقي الألوان المتقدم
        /// </summary>
        private void AdvancedPicker_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var advancedPicker = new AdvancedColorPickerWindow
                {
                    Owner = this,
                    SelectedColor = SelectedColor
                };

                if (advancedPicker.ShowDialog() == true)
                {
                    SelectedColor = advancedPicker.SelectedColor;
                    UpdateColorPreview();
                    UpdateColorValues();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح منتقي الألوان المتقدم: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}