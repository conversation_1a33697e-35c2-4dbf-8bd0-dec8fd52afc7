<Page x:Class="InjazAcc.UI.Views.Settings.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      xmlns:local="clr-namespace:InjazAcc.UI.Views.Settings"
      mc:Ignorable="d"
      d:DesignHeight="650" d:DesignWidth="1100"
      Title="صفحة الإعدادات">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- عنوان الصفحة -->
        <TextBlock Grid.Row="0" Text="إعدادات النظام" Style="{StaticResource PageTitle}"/>

        <!-- محتوى الصفحة -->
        <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}" FlowDirection="RightToLeft" SelectionChanged="TabControl_SelectionChanged">
            <!-- إعدادات الشركة -->
            <TabItem Header="معلومات الشركة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- بطاقة معلومات الشركة الأساسية -->
                        <materialDesign:Card Grid.Row="0" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="معلومات الشركة الأساسية" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- العمود الأول -->
                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="اسم الشركة" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtCompanyName" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,10">
                                        <TextBlock Text="الاسم القانوني" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtLegalName" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="2" Margin="0,0,10,10">
                                        <TextBlock Text="الرقم الضريبي" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtTaxNumber" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="3" Margin="0,0,10,10">
                                        <TextBlock Text="السجل التجاري" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtCommercialRegister" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="4" Margin="0,0,10,10">
                                        <TextBlock Text="تاريخ التأسيس" Style="{StaticResource FormLabel}"/>
                                        <DatePicker x:Name="dpEstablishmentDate"/>
                                    </StackPanel>

                                    <!-- العمود الثاني -->
                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="العنوان" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtAddress" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,0,0,10">
                                        <TextBlock Text="رقم الهاتف" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtPhone" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="2" Margin="10,0,0,10">
                                        <TextBlock Text="البريد الإلكتروني" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtEmail" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="3" Margin="10,0,0,10">
                                        <TextBlock Text="الموقع الإلكتروني" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtWebsite" Style="{StaticResource OutlinedTextBoxRTL}"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="4" Margin="10,0,0,10">
                                        <TextBlock Text="شعار الشركة" Style="{StaticResource FormLabel}"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBox Grid.Column="0" x:Name="txtLogoPath" Style="{StaticResource OutlinedTextBoxRTL}" IsReadOnly="True"/>
                                            <Button Grid.Column="1" Content="استعراض" Margin="10,0,0,0" Click="btnBrowseLogo_Click"/>
                                        </Grid>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة إعدادات العملة -->
                        <materialDesign:Card Grid.Row="1" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="إعدادات العملة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                        <TextBlock Text="العملة الأساسية" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbCurrency">
                                            <ComboBoxItem Content="ريال سعودي (SAR)" IsSelected="True"/>
                                            <ComboBoxItem Content="دولار أمريكي (USD)"/>
                                            <ComboBoxItem Content="يورو (EUR)"/>
                                            <ComboBoxItem Content="درهم إماراتي (AED)"/>
                                            <ComboBoxItem Content="دينار كويتي (KWD)"/>
                                            <ComboBoxItem Content="جنيه مصري (EGP)"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                        <TextBlock Text="رمز العملة" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtCurrencySymbol" Style="{StaticResource OutlinedTextBoxRTL}" Text="ر.س"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                            <Button x:Name="btnSaveCompanyInfo" Content="حفظ المعلومات" Style="{StaticResource ActionButton}" Click="btnSaveCompanyInfo_Click"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات الألوان والمظهر -->
            <TabItem Header="الألوان والمظهر">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- بطاقة الألوان الأساسية -->
                        <materialDesign:Card Grid.Row="0" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="الألوان الأساسية" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="اللون الأساسي" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbPrimaryColor" SelectionChanged="cmbPrimaryColor_SelectionChanged">
                                            <ComboBoxItem Content="أزرق فيروزي (Teal)" Tag="Teal" IsSelected="True"/>
                                            <ComboBoxItem Content="أزرق (Blue)" Tag="Blue"/>
                                            <ComboBoxItem Content="أخضر (Green)" Tag="Green"/>
                                            <ComboBoxItem Content="برتقالي (Orange)" Tag="Orange"/>
                                            <ComboBoxItem Content="أحمر (Red)" Tag="Red"/>
                                            <ComboBoxItem Content="بنفسجي (Purple)" Tag="Purple"/>
                                            <ComboBoxItem Content="وردي (Pink)" Tag="Pink"/>
                                            <ComboBoxItem Content="بني (Brown)" Tag="Brown"/>
                                            <ComboBoxItem Content="رمادي (Grey)" Tag="Grey"/>
                                            <ComboBoxItem Content="أزرق داكن (Indigo)" Tag="Indigo"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="اللون الثانوي" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbSecondaryColor" SelectionChanged="cmbSecondaryColor_SelectionChanged">
                                            <ComboBoxItem Content="كهرماني (Amber)" Tag="Amber" IsSelected="True"/>
                                            <ComboBoxItem Content="أصفر (Yellow)" Tag="Yellow"/>
                                            <ComboBoxItem Content="برتقالي (Orange)" Tag="Orange"/>
                                            <ComboBoxItem Content="أخضر فاتح (LightGreen)" Tag="LightGreen"/>
                                            <ComboBoxItem Content="أزرق فاتح (LightBlue)" Tag="LightBlue"/>
                                            <ComboBoxItem Content="وردي (Pink)" Tag="Pink"/>
                                            <ComboBoxItem Content="بنفسجي (Purple)" Tag="Purple"/>
                                            <ComboBoxItem Content="أحمر (Red)" Tag="Red"/>
                                            <ComboBoxItem Content="رمادي (Grey)" Tag="Grey"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,0">
                                        <TextBlock Text="المظهر العام" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbTheme" SelectionChanged="cmbTheme_SelectionChanged">
                                            <ComboBoxItem Content="فاتح (Light)" Tag="Light" IsSelected="True"/>
                                            <ComboBoxItem Content="داكن (Dark)" Tag="Dark"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,0,0,0">
                                        <TextBlock Text="معاينة الألوان" Style="{StaticResource FormLabel}"/>
                                        <Border x:Name="colorPreview" Height="40" CornerRadius="5" Margin="0,5,0,0">
                                            <Border.Background>
                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                    <GradientStop x:Name="primaryGradient" Color="#009688" Offset="0"/>
                                                    <GradientStop x:Name="secondaryGradient" Color="#FFC107" Offset="1"/>
                                                </LinearGradientBrush>
                                            </Border.Background>
                                            <TextBlock Text="معاينة الألوان" HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                     Foreground="White" FontWeight="Bold"/>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة ألوان مخصصة -->
                        <materialDesign:Card Grid.Row="1" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="ألوان مخصصة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="لون الخلفية" Style="{StaticResource FormLabel}"/>
                                        <Button x:Name="btnBackgroundColor" Content="اختيار اللون" Height="35" 
                                               Click="btnBackgroundColor_Click" Background="White" BorderBrush="Gray"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="5,0,5,10">
                                        <TextBlock Text="لون النص" Style="{StaticResource FormLabel}"/>
                                        <Button x:Name="btnTextColor" Content="اختيار اللون" Height="35" 
                                               Click="btnTextColor_Click" Background="Black" Foreground="White" BorderBrush="Gray"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="لون الحدود" Style="{StaticResource FormLabel}"/>
                                        <Button x:Name="btnBorderColor" Content="اختيار اللون" Height="35" 
                                               Click="btnBorderColor_Click" Background="Gray" BorderBrush="Gray"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,0">
                                        <TextBlock Text="لون الأزرار" Style="{StaticResource FormLabel}"/>
                                        <Button x:Name="btnButtonColor" Content="اختيار اللون" Height="35" 
                                               Click="btnButtonColor_Click" Background="Teal" Foreground="White" BorderBrush="Gray"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="5,0,5,0">
                                        <TextBlock Text="لون التحديد" Style="{StaticResource FormLabel}"/>
                                        <Button x:Name="btnSelectionColor" Content="اختيار اللون" Height="35" 
                                               Click="btnSelectionColor_Click" Background="LightBlue" BorderBrush="Gray"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2" Grid.Row="1" Margin="10,0,0,0">
                                        <TextBlock Text="لون التحذير" Style="{StaticResource FormLabel}"/>
                                        <Button x:Name="btnWarningColor" Content="اختيار اللون" Height="35" 
                                               Click="btnWarningColor_Click" Background="Orange" Foreground="White" BorderBrush="Gray"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة القوالب الجاهزة -->
                        <materialDesign:Card Grid.Row="2" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="القوالب الجاهزة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <WrapPanel Grid.Row="1" Orientation="Horizontal">
                                    <Button x:Name="btnClassicTheme" Content="كلاسيكي" Margin="5" Padding="15,8" 
                                           Click="btnClassicTheme_Click" Background="Navy" Foreground="White"/>
                                    <Button x:Name="btnModernTheme" Content="عصري" Margin="5" Padding="15,8" 
                                           Click="btnModernTheme_Click" Background="Teal" Foreground="White"/>
                                    <Button x:Name="btnWarmTheme" Content="دافئ" Margin="5" Padding="15,8" 
                                           Click="btnWarmTheme_Click" Background="Orange" Foreground="White"/>
                                    <Button x:Name="btnCoolTheme" Content="بارد" Margin="5" Padding="15,8" 
                                           Click="btnCoolTheme_Click" Background="Blue" Foreground="White"/>
                                    <Button x:Name="btnNatureTheme" Content="طبيعي" Margin="5" Padding="15,8" 
                                           Click="btnNatureTheme_Click" Background="Green" Foreground="White"/>
                                    <Button x:Name="btnElegantTheme" Content="أنيق" Margin="5" Padding="15,8" 
                                           Click="btnElegantTheme_Click" Background="Purple" Foreground="White"/>
                                </WrapPanel>
                            </Grid>
                        </materialDesign:Card>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                            <Button x:Name="btnApplyColors" Content="تطبيق الألوان" Style="{StaticResource ActionButton}" 
                                   Click="btnApplyColors_Click" Margin="5"/>
                            <Button x:Name="btnSaveColorSettings" Content="حفظ الإعدادات" Style="{StaticResource ActionButton}" 
                                   Click="btnSaveColorSettings_Click" Margin="5"/>
                            <Button x:Name="btnResetColors" Content="إعادة تعيين" Style="{StaticResource ActionButton}" 
                                   Click="btnResetColors_Click" Margin="5"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب التحكم المتقدم بالألوان -->
            <TabItem Header="التحكم المتقدم بالألوان">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="التحكم المتقدم بالألوان" Style="{StaticResource SectionTitle}" Margin="0,0,0,15"/>

                        <local:AdvancedColorControlPage Grid.Row="1" x:Name="advancedColorControl"/>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- تبويب اختبار الألوان -->
            <TabItem Header="اختبار الألوان">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="اختبار الألوان" Style="{StaticResource SectionTitle}" Margin="0,0,0,15"/>

                        <local:ColorTestPage Grid.Row="1" x:Name="colorTestPage"/>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات النظام العامة -->
            <TabItem Header="إعدادات عامة">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <Grid Margin="15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- بطاقة الإعدادات العامة -->
                        <materialDesign:Card Grid.Row="0" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="الإعدادات العامة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <StackPanel Grid.Row="1">
                                    <CheckBox x:Name="chkAutoBackup" Content="تفعيل النسخ الاحتياطي التلقائي" Margin="0,5"/>
                                    <CheckBox x:Name="chkShowWelcomeScreen" Content="عرض شاشة الترحيب عند بدء التشغيل" Margin="0,5"/>
                                    <CheckBox x:Name="chkAutoUpdate" Content="تفعيل التحديث التلقائي" Margin="0,5"/>
                                    <CheckBox x:Name="chkSendNotifications" Content="إرسال إشعارات النظام" Margin="0,5"/>
                                </StackPanel>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة إعدادات الفواتير -->
                        <materialDesign:Card Grid.Row="1" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="إعدادات الفواتير" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="بادئة رقم فاتورة المبيعات" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtSalesInvoicePrefix" Style="{StaticResource OutlinedTextBoxRTL}" Text="INV-"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="بادئة رقم فاتورة المشتريات" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtPurchaseInvoicePrefix" Style="{StaticResource OutlinedTextBoxRTL}" Text="PUR-"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,0">
                                        <TextBlock Text="نسبة الضريبة الافتراضية" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtDefaultTaxRate" Style="{StaticResource OutlinedTextBoxRTL}" Text="15"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,0,0,0">
                                        <TextBlock Text="فترة السداد الافتراضية (بالأيام)" Style="{StaticResource FormLabel}"/>
                                        <TextBox x:Name="txtDefaultPaymentPeriod" Style="{StaticResource OutlinedTextBoxRTL}" Text="30"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- بطاقة إعدادات الطباعة -->
                        <materialDesign:Card Grid.Row="2" Margin="0,0,0,15" Padding="15">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Text="إعدادات الطباعة" Style="{StaticResource SectionTitle}" Margin="0,0,0,10"/>

                                <Grid Grid.Row="1">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- الطابعة الافتراضية للفواتير -->
                                    <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                                        <TextBlock Text="طابعة الفواتير" Style="{StaticResource FormLabel}"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <ComboBox Grid.Column="0" x:Name="cmbInvoicePrinter"/>
                                            <Button Grid.Column="1" x:Name="btnRefreshInvoicePrinters" Margin="5,0,0,0"
                                                    ToolTip="تحديث قائمة الطابعات" Click="btnRefreshPrinters_Click">
                                                <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                                            </Button>
                                        </Grid>
                                    </StackPanel>

                                    <!-- الطابعة الافتراضية للتقارير -->
                                    <StackPanel Grid.Column="1" Grid.Row="0" Margin="10,0,0,10">
                                        <TextBlock Text="طابعة التقارير" Style="{StaticResource FormLabel}"/>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <ComboBox Grid.Column="0" x:Name="cmbReportPrinter"/>
                                            <Button Grid.Column="1" x:Name="btnRefreshReportPrinters" Margin="5,0,0,0"
                                                    ToolTip="تحديث قائمة الطابعات" Click="btnRefreshPrinters_Click">
                                                <materialDesign:PackIcon Kind="Refresh" Width="20" Height="20"/>
                                            </Button>
                                        </Grid>
                                    </StackPanel>

                                    <!-- حجم الورق للفواتير -->
                                    <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,10,10,10">
                                        <TextBlock Text="حجم ورق الفواتير" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbInvoicePaperSize">
                                            <ComboBoxItem Content="A4" IsSelected="True"/>
                                            <ComboBoxItem Content="A5"/>
                                            <ComboBoxItem Content="80mm (حراري)"/>
                                            <ComboBoxItem Content="58mm (حراري)"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <!-- حجم الورق للتقارير -->
                                    <StackPanel Grid.Column="1" Grid.Row="1" Margin="10,10,0,10">
                                        <TextBlock Text="حجم ورق التقارير" Style="{StaticResource FormLabel}"/>
                                        <ComboBox x:Name="cmbReportPaperSize">
                                            <ComboBoxItem Content="A4" IsSelected="True"/>
                                            <ComboBoxItem Content="A5"/>
                                            <ComboBoxItem Content="Letter"/>
                                            <ComboBoxItem Content="Legal"/>
                                        </ComboBox>
                                    </StackPanel>

                                    <!-- إعدادات إضافية للطباعة -->
                                    <StackPanel Grid.Column="0" Grid.ColumnSpan="2" Grid.Row="2" Margin="0,10,0,0">
                                        <CheckBox x:Name="chkPrintLogo" Content="طباعة شعار الشركة على الفواتير" Margin="0,5" IsChecked="True"/>
                                        <CheckBox x:Name="chkPrintPreview" Content="عرض معاينة قبل الطباعة" Margin="0,5" IsChecked="True"/>
                                        <CheckBox x:Name="chkAutoPrint" Content="طباعة تلقائية عند حفظ الفاتورة" Margin="0,5"/>
                                    </StackPanel>
                                </Grid>
                            </Grid>
                        </materialDesign:Card>

                        <!-- أزرار الإجراءات -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
                            <Button x:Name="btnSaveGeneralSettings" Content="حفظ الإعدادات" Style="{StaticResource ActionButton}" Click="btnSaveGeneralSettings_Click"/>
                            <Button x:Name="btnResetSettings" Content="استعادة الإعدادات الافتراضية" Style="{StaticResource ActionButton}" Margin="10,0,0,0" Click="btnResetSettings_Click"/>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </TabItem>

            <!-- إعدادات المستخدمين -->
            <TabItem Header="المستخدمين والصلاحيات">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- عنوان القسم -->
                    <TextBlock Grid.Row="0" Text="إدارة المستخدمين والصلاحيات" Style="{StaticResource SectionTitle}" Margin="0,0,0,15"/>

                    <!-- محتوى القسم -->
                    <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                        <!-- قسم المستخدمين -->
                        <TabItem Header="المستخدمين">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- شريط البحث والإضافة -->
                                <Grid Grid.Row="0" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0" x:Name="txtSearchUser" Style="{StaticResource OutlinedTextBoxRTL}"
                                             materialDesign:HintAssist.Hint="بحث عن مستخدم..." Margin="0,0,10,0"
                                             KeyUp="txtSearchUser_KeyUp"/>

                                    <Button Grid.Column="1" x:Name="btnAddUser" Content="إضافة مستخدم جديد"
                                            Style="{StaticResource ActionButton}" Click="btnAddUser_Click"/>
                                </Grid>

                                <!-- جدول المستخدمين -->
                                <DataGrid Grid.Row="1" x:Name="dgUsers" AutoGenerateColumns="False" IsReadOnly="False"
                                          Style="{StaticResource MaterialDesignDataGrid}" SelectionMode="Single"
                                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                                          CanUserResizeRows="False" CanUserSortColumns="True"
                                          MouseDoubleClick="dgUsers_MouseDoubleClick"
                                          CellEditEnding="dgUsers_CellEditEnding">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="*"/>
                                        <DataGridTextColumn Header="الاسم الكامل" Binding="{Binding FullName}" Width="*"/>
                                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="*"/>
                                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="*"/>
                                        <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="70"/>
                                        <DataGridTemplateColumn Header="الإجراءات" Width="120" IsReadOnly="True">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="تعديل" Margin="0,0,5,0"
                                                                Click="btnEditUser_Click">
                                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="حذف" Foreground="Red"
                                                                Click="btnDeleteUser_Click">
                                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <!-- إحصائيات المستخدمين -->
                                <Grid Grid.Row="2" Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="5" Margin="0,0,5,0" Padding="10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountMultiple" Width="24" Height="24" VerticalAlignment="Center" Foreground="#1976D2"/>
                                            <TextBlock Text="إجمالي المستخدمين: " Margin="5,0" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="txtTotalUsers" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="1" Background="#E8F5E9" CornerRadius="5" Margin="5,0" Padding="10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountCheck" Width="24" Height="24" VerticalAlignment="Center" Foreground="#388E3C"/>
                                            <TextBlock Text="المستخدمين النشطين: " Margin="5,0" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="txtActiveUsers" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>

                                    <Border Grid.Column="2" Background="#FFEBEE" CornerRadius="5" Margin="5,0,0,0" Padding="10">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <materialDesign:PackIcon Kind="AccountOff" Width="24" Height="24" VerticalAlignment="Center" Foreground="#D32F2F"/>
                                            <TextBlock Text="المستخدمين غير النشطين: " Margin="5,0" VerticalAlignment="Center"/>
                                            <TextBlock x:Name="txtInactiveUsers" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </Grid>
                            </Grid>
                        </TabItem>

                        <!-- قسم الأدوار -->
                        <TabItem Header="الأدوار والصلاحيات">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- شريط البحث والإضافة -->
                                <Grid Grid.Row="0" Margin="0,0,0,10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0" x:Name="txtSearchRole" Style="{StaticResource OutlinedTextBoxRTL}"
                                             materialDesign:HintAssist.Hint="بحث عن دور..." Margin="0,0,10,0"
                                             KeyUp="txtSearchRole_KeyUp"/>

                                    <Button Grid.Column="1" x:Name="btnAddRole" Content="إضافة دور جديد"
                                            Style="{StaticResource ActionButton}" Click="btnAddRole_Click"/>
                                </Grid>

                                <!-- جدول الأدوار -->
                                <DataGrid Grid.Row="1" x:Name="dgRoles" AutoGenerateColumns="False" IsReadOnly="True"
                                          Style="{StaticResource MaterialDesignDataGrid}" SelectionMode="Single"
                                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                                          CanUserResizeRows="False" CanUserSortColumns="True"
                                          MouseDoubleClick="dgRoles_MouseDoubleClick">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم الدور" Binding="{Binding Name}" Width="*"/>
                                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="2*"/>
                                        <DataGridTemplateColumn Header="الإجراءات" Width="180">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="تعديل" Margin="0,0,5,0"
                                                                Click="btnEditRole_Click">
                                                            <materialDesign:PackIcon Kind="Edit" Width="18" Height="18"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="إدارة الصلاحيات" Margin="0,0,5,0" Foreground="#1976D2"
                                                                Click="btnManagePermissions_Click">
                                                            <materialDesign:PackIcon Kind="ShieldAccount" Width="18" Height="18"/>
                                                        </Button>
                                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                                                ToolTip="حذف" Foreground="Red"
                                                                Click="btnDeleteRole_Click">
                                                            <materialDesign:PackIcon Kind="Delete" Width="18" Height="18"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>

                                <!-- إحصائيات الأدوار -->
                                <Border Grid.Row="2" Background="#E3F2FD" CornerRadius="5" Margin="0,10,0,0" Padding="10">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <materialDesign:PackIcon Kind="ShieldAccount" Width="24" Height="24" VerticalAlignment="Center" Foreground="#1976D2"/>
                                        <TextBlock Text="إجمالي الأدوار: " Margin="5,0" VerticalAlignment="Center"/>
                                        <TextBlock x:Name="txtTotalRoles" Text="0" FontWeight="Bold" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </TabItem>

                        <!-- قسم الصلاحيات -->
                        <TabItem Header="قائمة الصلاحيات">
                            <Grid Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- شريط البحث -->
                                <TextBox Grid.Row="0" x:Name="txtSearchPermission" Style="{StaticResource OutlinedTextBoxRTL}"
                                         materialDesign:HintAssist.Hint="بحث عن صلاحية..." Margin="0,0,0,10"
                                         KeyUp="txtSearchPermission_KeyUp"/>

                                <!-- جدول الصلاحيات -->
                                <DataGrid Grid.Row="1" x:Name="dgPermissions" AutoGenerateColumns="False" IsReadOnly="True"
                                          Style="{StaticResource MaterialDesignDataGrid}" SelectionMode="Single"
                                          CanUserAddRows="False" CanUserDeleteRows="False" CanUserReorderColumns="False"
                                          CanUserResizeRows="False" CanUserSortColumns="True">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="اسم الصلاحية" Binding="{Binding Name}" Width="*"/>
                                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="2*"/>
                                        <DataGridTextColumn Header="الوحدة" Binding="{Binding Module}" Width="*"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </TabItem>
                    </TabControl>
                </Grid>
            </TabItem>

            <!-- النسخ الاحتياطي -->
            <TabItem Header="النسخ الاحتياطي">
                <Grid Margin="15">
                    <TextBlock Text="سيتم تطوير هذا القسم في الإصدار القادم" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="16" Foreground="Gray"/>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</Page>
