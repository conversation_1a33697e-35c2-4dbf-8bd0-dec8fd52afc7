@echo off
chcp 65001 >nul
title برنامج إنجاز المحاسبي - نظام التحكم الشامل بالألوان

echo.
echo ================================================================
echo 🎨 برنامج إنجاز المحاسبي - نظام التحكم الشامل بالألوان
echo ================================================================
echo.
echo ✨ المميزات الجديدة:
echo    • التحكم في +80 لون مختلف
echo    • 8 قوالب ألوان جاهزة  
echo    • منتقي ألوان متقدم
echo    • معاينة فورية للتغييرات
echo    • تصدير واستيراد الإعدادات
echo.
echo ================================================================
echo.

REM التحقق من وجود .NET
echo 🔍 جاري التحقق من .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ لم يتم العثور على .NET
    echo.
    echo يرجى تثبيت .NET من الرابط التالي:
    echo https://dotnet.microsoft.com/download
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('dotnet --version 2^>nul') do set dotnet_version=%%i
echo ✅ تم العثور على .NET إصدار: %dotnet_version%

REM التحقق من وجود المشروع
echo.
echo 🔍 جاري التحقق من ملفات المشروع...
if not exist "injaz_acc" (
    echo ❌ مجلد المشروع غير موجود
    pause
    exit /b 1
)

if not exist "injaz_acc\src\InjazAcc.UI\InjazAcc.UI.csproj" (
    echo ❌ ملف المشروع غير موجود
    pause
    exit /b 1
)

echo ✅ تم العثور على ملف المشروع

REM استعادة الحزم
echo.
echo 📦 جاري استعادة حزم NuGet...
dotnet restore "injaz_acc\src\InjazAcc.UI\InjazAcc.UI.csproj" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في استعادة الحزم
    pause
    exit /b 1
)
echo ✅ تم استعادة الحزم بنجاح

REM بناء المشروع
echo.
echo 🔨 جاري بناء المشروع...
dotnet build "injaz_acc\src\InjazAcc.UI\InjazAcc.UI.csproj" --configuration Release >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo جاري المحاولة مع إظهار التفاصيل...
    dotnet build "injaz_acc\src\InjazAcc.UI\InjazAcc.UI.csproj" --configuration Release
    pause
    exit /b 1
)
echo ✅ تم بناء المشروع بنجاح

REM تشغيل البرنامج
echo.
echo 🚀 جاري تشغيل البرنامج...
echo ================================================================
echo.

cd "injaz_acc\src\InjazAcc.UI"
dotnet run --configuration Release

echo.
echo ================================================================
echo ✅ تم إغلاق البرنامج
echo.
echo شكراً لاستخدام برنامج إنجاز المحاسبي! 🙏
echo.
pause