using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.Services;

namespace InjazAcc.UI.Views.Inventory
{
    /// <summary>
    /// Interaction logic for InventoryPage.xaml
    /// </summary>
    public partial class InventoryPage : Page
    {
        private ObservableCollection<InventoryItem> _inventoryItems;
        private ObservableCollection<InventoryCountItem> _inventoryCountItems;
        private ObservableCollection<InventoryMovementItem> _inventoryMovementItems;

        public InventoryPage()
        {
            InitializeComponent();
            LoadSampleData();

            // تعيين التواريخ الافتراضية
            dpInventoryDate.SelectedDate = DateTime.Now;
            dpFromDate.SelectedDate = DateTime.Now.AddDays(-30);
            dpToDate.SelectedDate = DateTime.Now;
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للمخزون
                _inventoryItems = new ObservableCollection<InventoryItem>
                {
                    new InventoryItem { Code = "P001", Name = "لابتوب HP ProBook", Unit = "قطعة", AvailableQuantity = 15, PurchasePrice = 3000, SalePrice = 3500, TotalValue = 45000 },
                    new InventoryItem { Code = "P002", Name = "طابعة Canon", Unit = "قطعة", AvailableQuantity = 8, PurchasePrice = 600, SalePrice = 750, TotalValue = 4800 },
                    new InventoryItem { Code = "P003", Name = "ماوس لاسلكي", Unit = "قطعة", AvailableQuantity = 25, PurchasePrice = 50, SalePrice = 80, TotalValue = 1250 },
                    new InventoryItem { Code = "P004", Name = "لوحة مفاتيح", Unit = "قطعة", AvailableQuantity = 20, PurchasePrice = 70, SalePrice = 100, TotalValue = 1400 },
                    new InventoryItem { Code = "P005", Name = "شاشة LG", Unit = "قطعة", AvailableQuantity = 10, PurchasePrice = 800, SalePrice = 950, TotalValue = 8000 }
                };
                dgInventory.ItemsSource = _inventoryItems;

                // بيانات تجريبية للجرد
                _inventoryCountItems = new ObservableCollection<InventoryCountItem>
                {
                    new InventoryCountItem { Code = "P001", Name = "لابتوب HP ProBook", Unit = "قطعة", SystemQuantity = 15, ActualQuantity = 15, Difference = 0, Notes = "" },
                    new InventoryCountItem { Code = "P002", Name = "طابعة Canon", Unit = "قطعة", SystemQuantity = 8, ActualQuantity = 7, Difference = -1, Notes = "وجود تلف في إحدى الطابعات" },
                    new InventoryCountItem { Code = "P003", Name = "ماوس لاسلكي", Unit = "قطعة", SystemQuantity = 25, ActualQuantity = 26, Difference = 1, Notes = "وجود وحدة إضافية" },
                    new InventoryCountItem { Code = "P004", Name = "لوحة مفاتيح", Unit = "قطعة", SystemQuantity = 20, ActualQuantity = 20, Difference = 0, Notes = "" },
                    new InventoryCountItem { Code = "P005", Name = "شاشة LG", Unit = "قطعة", SystemQuantity = 10, ActualQuantity = 9, Difference = -1, Notes = "وجود تلف في إحدى الشاشات" }
                };
                dgInventoryCount.ItemsSource = _inventoryCountItems;

                // بيانات تجريبية لحركة المخزون
                _inventoryMovementItems = new ObservableCollection<InventoryMovementItem>
                {
                    new InventoryMovementItem { Date = DateTime.Now.AddDays(-1), DocumentNumber = "PUR-001", MovementType = "شراء", ItemName = "لابتوب HP ProBook", InQuantity = 5, OutQuantity = 0, Balance = 15 },
                    new InventoryMovementItem { Date = DateTime.Now.AddDays(-2), DocumentNumber = "SAL-001", MovementType = "بيع", ItemName = "لابتوب HP ProBook", InQuantity = 0, OutQuantity = 2, Balance = 10 },
                    new InventoryMovementItem { Date = DateTime.Now.AddDays(-3), DocumentNumber = "PUR-002", MovementType = "شراء", ItemName = "طابعة Canon", InQuantity = 3, OutQuantity = 0, Balance = 8 },
                    new InventoryMovementItem { Date = DateTime.Now.AddDays(-4), DocumentNumber = "SAL-002", MovementType = "بيع", ItemName = "طابعة Canon", InQuantity = 0, OutQuantity = 1, Balance = 5 },
                    new InventoryMovementItem { Date = DateTime.Now.AddDays(-5), DocumentNumber = "ADJ-001", MovementType = "تسوية", ItemName = "ماوس لاسلكي", InQuantity = 1, OutQuantity = 0, Balance = 25 }
                };
                dgInventoryMovement.ItemsSource = _inventoryMovementItems;

                // إضافة الأصناف إلى قائمة الاختيار
                cmbItems.Items.Clear();
                cmbItems.Items.Add(new ComboBoxItem { Content = "جميع الأصناف" });
                foreach (var item in _inventoryItems)
                {
                    cmbItems.Items.Add(new ComboBoxItem { Content = item.Name });
                }
                cmbItems.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region المخزون الرئيسي

        private void cmbWarehouses_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            try
            {
                // تحديث عرض المخزون حسب المخزن المختار
                string selectedWarehouse = (cmbWarehouses.SelectedItem as ComboBoxItem)?.Content.ToString();

                // في التطبيق الحقيقي، سيتم تحميل بيانات المخزن المختار من قاعدة البيانات
                // تحديث عنوان الصفحة ليعكس المخزن المختار
                // LoadWarehouseData(selectedWarehouse);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddWarehouse_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إضافة مخزن جديد
                MessageBox.Show("سيتم فتح نافذة إضافة مخزن جديد", "إضافة مخزن", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إنشاء نافذة WarehouseWindow
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // البحث في المخزون
                string searchText = txtSearch.Text.Trim();
                if (string.IsNullOrEmpty(searchText))
                {
                    dgInventory.ItemsSource = _inventoryItems;
                    return;
                }

                var filteredItems = new ObservableCollection<InventoryItem>();
                foreach (var item in _inventoryItems)
                {
                    if (item.Code.Contains(searchText) || item.Name.Contains(searchText))
                    {
                        filteredItems.Add(item);
                    }
                }

                dgInventory.ItemsSource = filteredItems;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة إضافة صنف جديد
                MessageBox.Show("سيتم فتح نافذة إضافة صنف جديد", "إضافة صنف", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إنشاء نافذة InventoryItemWindow
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعديل الصنف المحدد
                var button = sender as Button;
                var item = button.DataContext as InventoryItem;

                if (item != null)
                {
                    var itemWindow = new InventoryItemWindow(item);
                    if (itemWindow.ShowDialog() == true)
                    {
                        // تحديث الصنف في المخزون
                        int index = _inventoryItems.IndexOf(item);
                        if (index >= 0)
                        {
                            _inventoryItems[index] = itemWindow.Item;
                            dgInventory.Items.Refresh();
                            MessageBox.Show("تم تعديل الصنف بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حذف الصنف المحدد
                var button = sender as Button;
                var item = button.DataContext as InventoryItem;

                if (item != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الصنف: {item.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _inventoryItems.Remove(item);
                        MessageBox.Show("تم حذف الصنف بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnItemDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض تفاصيل الصنف
                var button = sender as Button;
                var item = button.DataContext as InventoryItem;

                if (item != null)
                {
                    var itemDetailsWindow = new InventoryItemDetailsWindow(item);
                    itemDetailsWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void btnExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_inventoryItems == null || _inventoryItems.Count == 0)
                {
                    MessageBox.Show("لا توجد بيانات مخزون للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تحويل بيانات المخزون إلى نموذج Core
                var coreProducts = _inventoryItems.Select(item => new InjazAcc.Core.Models.Product
                {
                    Code = item.Code,
                    Name = item.Name,
                    Description = "", // InventoryItem doesn't have Description
                    PurchasePrice = (decimal)item.PurchasePrice,
                    SalePrice = (decimal)item.SalePrice,
                    MinimumStock = 0, // InventoryItem doesn't have MinimumStock
                    IsActive = true,
                    CreatedAt = DateTime.Now
                }).ToList();

                // تصدير البيانات إلى Excel (استخدام الخدمة البديلة)
                bool success = await AlternativeExcelExportService.ExportProductsToExcel(coreProducts);

                if (success)
                {
                    MessageBox.Show("تم تصدير بيانات المخزون بنجاح!", "نجح التصدير",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تصدير البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region جرد المخزون

        private void btnStartNewInventory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // بدء جرد جديد
                MessageBox.Show("تم بدء جرد جديد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                // إعادة تعيين قيم الجرد الفعلية
                foreach (var item in _inventoryCountItems)
                {
                    item.ActualQuantity = 0;
                    item.Difference = -item.SystemQuantity;
                    item.Notes = "";
                }

                dgInventoryCount.Items.Refresh();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSaveInventory_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // حفظ الجرد
                MessageBox.Show("تم حفظ الجرد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAdjustDifferences_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تسوية فروقات الجرد
                var result = MessageBox.Show("هل أنت متأكد من تسوية فروقات الجرد؟ سيتم تعديل كميات المخزون لتتطابق مع الكميات الفعلية.", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // تحديث كميات المخزون
                    foreach (var countItem in _inventoryCountItems)
                    {
                        foreach (var inventoryItem in _inventoryItems)
                        {
                            if (countItem.Code == inventoryItem.Code)
                            {
                                inventoryItem.AvailableQuantity = countItem.ActualQuantity;
                                inventoryItem.TotalValue = inventoryItem.AvailableQuantity * inventoryItem.PurchasePrice;
                                break;
                            }
                        }
                    }

                    dgInventory.Items.Refresh();
                    MessageBox.Show("تم تسوية فروقات الجرد بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintInventoryReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // طباعة تقرير الجرد
                MessageBox.Show("جاري طباعة تقرير الجرد...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region حركة المخزون

        private void btnShowMovement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض حركة المخزون
                string selectedWarehouse = (cmbMovementWarehouses.SelectedItem as ComboBoxItem)?.Content.ToString();
                string selectedItem = (cmbItems.SelectedItem as ComboBoxItem)?.Content.ToString();
                DateTime? fromDate = dpFromDate.SelectedDate;
                DateTime? toDate = dpToDate.SelectedDate;

                if (fromDate == null || toDate == null)
                {
                    MessageBox.Show("الرجاء تحديد فترة التقرير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (fromDate > toDate)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تصفية البيانات حسب المعايير المحددة
                var filteredItems = new ObservableCollection<InventoryMovementItem>();
                foreach (var item in _inventoryMovementItems)
                {
                    if (item.Date >= fromDate && item.Date <= toDate)
                    {
                        if (selectedItem == "جميع الأصناف" || item.ItemName == selectedItem)
                        {
                            filteredItems.Add(item);
                        }
                    }
                }

                dgInventoryMovement.ItemsSource = filteredItems;

                MessageBox.Show($"تم عرض حركة المخزون للفترة من {fromDate.Value.ToString("yyyy-MM-dd")} إلى {toDate.Value.ToString("yyyy-MM-dd")}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }

    public class InventoryItem
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public double AvailableQuantity { get; set; }
        public double PurchasePrice { get; set; }
        public double SalePrice { get; set; }
        public double TotalValue { get; set; }
    }

    public class InventoryCountItem
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Unit { get; set; }
        public double SystemQuantity { get; set; }
        public double ActualQuantity { get; set; }
        public double Difference { get; set; }
        public string Notes { get; set; }
    }

    public class InventoryMovementItem
    {
        public DateTime Date { get; set; }
        public string DocumentNumber { get; set; }
        public string MovementType { get; set; }
        public string ItemName { get; set; }
        public double InQuantity { get; set; }
        public double OutQuantity { get; set; }
        public double Balance { get; set; }
    }
}
