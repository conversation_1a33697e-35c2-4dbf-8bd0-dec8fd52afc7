using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Data.Sqlite;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Helpers
{
    /// <summary>
    /// مدير الإعدادات المحسن مع ضمان الحفظ
    /// </summary>
    public static class EnhancedSettingsManager
    {
        private static readonly string SettingsDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "InjazAcc");
        
        private static readonly string DatabasePath = Path.Combine(SettingsDirectory, "settings.db");
        private static readonly string BackupPath = Path.Combine(SettingsDirectory, "settings_backup.db");
        private static readonly string JsonBackupPath = Path.Combine(SettingsDirectory, "settings_backup.json");

        /// <summary>
        /// تهيئة مدير الإعدادات
        /// </summary>
        public static async Task InitializeAsync()
        {
            try
            {
                // إنشاء المجلد إذا لم يكن موجوداً
                if (!Directory.Exists(SettingsDirectory))
                {
                    Directory.CreateDirectory(SettingsDirectory);
                    Console.WriteLine($"✅ تم إنشاء مجلد الإعدادات: {SettingsDirectory}");
                }

                // تهيئة قاعدة البيانات
                await InitializeDatabaseAsync();
                
                Console.WriteLine("✅ تم تهيئة مدير الإعدادات المحسن");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تهيئة مدير الإعدادات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تهيئة قاعدة بيانات الإعدادات
        /// </summary>
        private static async Task InitializeDatabaseAsync()
        {
            using var connection = new SqliteConnection($"Data Source={DatabasePath}");
            await connection.OpenAsync();
            
            var createTableCmd = connection.CreateCommand();
            createTableCmd.CommandText = @"
                CREATE TABLE IF NOT EXISTS SystemSettings (
                    Id INTEGER PRIMARY KEY,
                    SettingKey TEXT NOT NULL UNIQUE,
                    SettingValue TEXT,
                    Description TEXT,
                    [Group] INTEGER,
                    IsReadOnly INTEGER DEFAULT 0,
                    CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_setting_key ON SystemSettings(SettingKey);
                CREATE INDEX IF NOT EXISTS idx_setting_group ON SystemSettings([Group]);
            ";
            await createTableCmd.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// حفظ الإعدادات مع النسخ الاحتياطي
        /// </summary>
        public static async Task<bool> SaveSettingsAsync(List<SystemSettings> settings)
        {
            try
            {
                // إنشاء نسخة احتياطية أولاً
                await CreateBackupAsync();

                using var connection = new SqliteConnection($"Data Source={DatabasePath}");
                await connection.OpenAsync();
                
                using var transaction = (SqliteTransaction)await connection.BeginTransactionAsync();
                
                try
                {
                    foreach (var setting in settings)
                    {
                        var cmd = connection.CreateCommand();
                        cmd.Transaction = transaction;
                        cmd.CommandText = @"
                            INSERT INTO SystemSettings (Id, SettingKey, SettingValue, Description, [Group], IsReadOnly, UpdatedAt)
                            VALUES ($id, $key, $value, $desc, $group, $readonly, $updated)
                            ON CONFLICT(SettingKey) DO UPDATE SET
                                SettingValue=excluded.SettingValue,
                                Description=excluded.Description,
                                [Group]=excluded.[Group],
                                IsReadOnly=excluded.IsReadOnly,
                                UpdatedAt=excluded.UpdatedAt;";
                        
                        cmd.Parameters.AddWithValue("$id", setting.Id);
                        cmd.Parameters.AddWithValue("$key", setting.SettingKey ?? string.Empty);
                        cmd.Parameters.AddWithValue("$value", setting.SettingValue ?? string.Empty);
                        cmd.Parameters.AddWithValue("$desc", setting.Description ?? string.Empty);
                        cmd.Parameters.AddWithValue("$group", (int)setting.Group);
                        cmd.Parameters.AddWithValue("$readonly", setting.IsReadOnly ? 1 : 0);
                        cmd.Parameters.AddWithValue("$updated", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        
                        await cmd.ExecuteNonQueryAsync();
                    }
                    
                    await transaction.CommitAsync();
                    
                    // التحقق من الحفظ
                    var savedCount = await GetSettingsCountAsync();
                    Console.WriteLine($"✅ تم حفظ {settings.Count} إعداد، المجموع في قاعدة البيانات: {savedCount}");
                    
                    // حفظ نسخة JSON احتياطية
                    await SaveJsonBackupAsync(settings);
                    
                    return true;
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في حفظ الإعدادات: {ex.Message}");
                
                // محاولة استعادة النسخة الاحتياطية
                await RestoreBackupAsync();
                return false;
            }
        }

        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        public static async Task<List<SystemSettings>> LoadSettingsAsync()
        {
            var settings = new List<SystemSettings>();
            
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabasePath}");
                await connection.OpenAsync();
                
                var cmd = connection.CreateCommand();
                cmd.CommandText = @"
                    SELECT Id, SettingKey, SettingValue, Description, [Group], IsReadOnly, CreatedAt, UpdatedAt 
                    FROM SystemSettings 
                    ORDER BY [Group], SettingKey";
                
                using var reader = await cmd.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    settings.Add(new SystemSettings
                    {
                        Id = reader.GetInt32(0),
                        SettingKey = reader.IsDBNull(1) ? string.Empty : reader.GetString(1),
                        SettingValue = reader.IsDBNull(2) ? null : reader.GetString(2),
                        Description = reader.IsDBNull(3) ? null : reader.GetString(3),
                        Group = (SettingGroup)reader.GetInt32(4),
                        IsReadOnly = reader.GetInt32(5) == 1
                    });
                }
                
                Console.WriteLine($"✅ تم تحميل {settings.Count} إعداد");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحميل الإعدادات: {ex.Message}");
                
                // محاولة تحميل النسخة الاحتياطية
                settings = await LoadJsonBackupAsync();
            }
            
            return settings;
        }

        /// <summary>
        /// حفظ إعداد واحد
        /// </summary>
        public static async Task<bool> SaveSettingAsync(string key, string value, string description = null, SettingGroup group = SettingGroup.General)
        {
            var setting = new SystemSettings
            {
                SettingKey = key,
                SettingValue = value,
                Description = description,
                Group = group,
                IsReadOnly = false
            };
            
            return await SaveSettingsAsync(new List<SystemSettings> { setting });
        }

        /// <summary>
        /// الحصول على إعداد واحد
        /// </summary>
        public static async Task<string> GetSettingAsync(string key, string defaultValue = null)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabasePath}");
                await connection.OpenAsync();
                
                var cmd = connection.CreateCommand();
                cmd.CommandText = "SELECT SettingValue FROM SystemSettings WHERE SettingKey = $key";
                cmd.Parameters.AddWithValue("$key", key);
                
                var result = await cmd.ExecuteScalarAsync();
                return result?.ToString() ?? defaultValue;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الحصول على الإعداد {key}: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية
        /// </summary>
        private static Task CreateBackupAsync()
        {
            try
            {
                if (File.Exists(DatabasePath))
                {
                    File.Copy(DatabasePath, BackupPath, true);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ تحذير: لا يمكن إنشاء نسخة احتياطية: {ex.Message}");
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// استعادة النسخة الاحتياطية
        /// </summary>
        private static Task RestoreBackupAsync()
        {
            try
            {
                if (File.Exists(BackupPath))
                {
                    File.Copy(BackupPath, DatabasePath, true);
                    Console.WriteLine("✅ تم استعادة النسخة الاحتياطية");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في استعادة النسخة الاحتياطية: {ex.Message}");
            }
            return Task.CompletedTask;
        }

        /// <summary>
        /// حفظ نسخة احتياطية JSON
        /// </summary>
        private static async Task SaveJsonBackupAsync(List<SystemSettings> settings)
        {
            try
            {
                var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                await File.WriteAllTextAsync(JsonBackupPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ تحذير: لا يمكن حفظ النسخة الاحتياطية JSON: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل النسخة الاحتياطية JSON
        /// </summary>
        private static async Task<List<SystemSettings>> LoadJsonBackupAsync()
        {
            try
            {
                if (File.Exists(JsonBackupPath))
                {
                    var json = await File.ReadAllTextAsync(JsonBackupPath);
                    var settings = JsonSerializer.Deserialize<List<SystemSettings>>(json) ?? new List<SystemSettings>();
                    Console.WriteLine($"✅ تم تحميل {settings.Count} إعداد من النسخة الاحتياطية JSON");
                    return settings;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحميل النسخة الاحتياطية JSON: {ex.Message}");
            }
            
            return new List<SystemSettings>();
        }

        /// <summary>
        /// الحصول على عدد الإعدادات
        /// </summary>
        private static async Task<int> GetSettingsCountAsync()
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabasePath}");
                await connection.OpenAsync();
                
                var cmd = connection.CreateCommand();
                cmd.CommandText = "SELECT COUNT(*) FROM SystemSettings";
                
                var result = await cmd.ExecuteScalarAsync();
                return Convert.ToInt32(result);
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// التحقق من سلامة قاعدة البيانات
        /// </summary>
        public static async Task<bool> VerifyDatabaseIntegrityAsync()
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabasePath}");
                await connection.OpenAsync();
                
                var cmd = connection.CreateCommand();
                cmd.CommandText = "PRAGMA integrity_check";
                
                var result = await cmd.ExecuteScalarAsync();
                return result?.ToString() == "ok";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في التحقق من سلامة قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تنظيف الإعدادات القديمة
        /// </summary>
        public static async Task CleanupOldSettingsAsync(int daysOld = 30)
        {
            try
            {
                using var connection = new SqliteConnection($"Data Source={DatabasePath}");
                await connection.OpenAsync();
                
                var cmd = connection.CreateCommand();
                cmd.CommandText = @"
                    DELETE FROM SystemSettings 
                    WHERE UpdatedAt < datetime('now', '-' || $days || ' days')
                    AND IsReadOnly = 0";
                cmd.Parameters.AddWithValue("$days", daysOld);
                
                var deletedCount = await cmd.ExecuteNonQueryAsync();
                if (deletedCount > 0)
                {
                    Console.WriteLine($"✅ تم حذف {deletedCount} إعداد قديم");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تنظيف الإعدادات القديمة: {ex.Message}");
            }
        }
    }
}