using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace InjazAcc.UI.Helpers
{
    /// <summary>
    /// مدير ألوان الأقسام
    /// </summary>
    public static class SectionColorManager
    {
        /// <summary>
        /// أسماء الأقسام
        /// </summary>
        public enum SectionType
        {
            Sales,      // المبيعات
            Purchases,  // المشتريات
            Inventory,  // المخزون
            Customers,  // العملاء
            Suppliers,  // الموردين
            Accounting, // المحاسبة
            Reports,    // التقارير
            Settings    // الإعدادات
        }

        /// <summary>
        /// الحصول على لون قسم معين
        /// </summary>
        public static Color GetSectionColor(SectionType section)
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                
                return section switch
                {
                    SectionType.Sales => (Color)ColorConverter.ConvertFromString(settings.SalesColor),
                    SectionType.Purchases => (Color)ColorConverter.ConvertFromString(settings.PurchasesColor),
                    SectionType.Inventory => (Color)ColorConverter.ConvertFromString(settings.InventoryColor),
                    SectionType.Customers => (Color)ColorConverter.ConvertFromString(settings.CustomersColor),
                    SectionType.Suppliers => (Color)ColorConverter.ConvertFromString(settings.SuppliersColor),
                    SectionType.Accounting => (Color)ColorConverter.ConvertFromString(settings.AccountingColor),
                    SectionType.Reports => (Color)ColorConverter.ConvertFromString(settings.ReportsColor),
                    SectionType.Settings => (Color)ColorConverter.ConvertFromString(settings.SettingsColor),
                    _ => (Color)ColorConverter.ConvertFromString(settings.PrimaryColor)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على لون القسم: {ex.Message}");
                return GetDefaultSectionColor(section);
            }
        }

        /// <summary>
        /// الحصول على لون القسم الافتراضي
        /// </summary>
        public static Color GetDefaultSectionColor(SectionType section)
        {
            return section switch
            {
                SectionType.Sales => Color.FromRgb(76, 175, 80),      // أخضر
                SectionType.Purchases => Color.FromRgb(255, 152, 0),  // برتقالي
                SectionType.Inventory => Color.FromRgb(33, 150, 243), // أزرق
                SectionType.Customers => Color.FromRgb(156, 39, 176), // بنفسجي
                SectionType.Suppliers => Color.FromRgb(255, 87, 34),  // أحمر برتقالي
                SectionType.Accounting => Color.FromRgb(121, 85, 72), // بني
                SectionType.Reports => Color.FromRgb(96, 125, 139),   // رمادي مزرق
                SectionType.Settings => Color.FromRgb(158, 158, 158), // رمادي
                _ => Color.FromRgb(0, 150, 136)                       // تيل
            };
        }

        /// <summary>
        /// الحصول على فرشاة قسم معين
        /// </summary>
        public static SolidColorBrush GetSectionBrush(SectionType section)
        {
            return new SolidColorBrush(GetSectionColor(section));
        }

        /// <summary>
        /// الحصول على لون قسم من النص
        /// </summary>
        public static Color GetSectionColorFromText(string sectionName)
        {
            try
            {
                var section = sectionName.ToLower() switch
                {
                    "sales" or "مبيعات" or "المبيعات" => SectionType.Sales,
                    "purchases" or "مشتريات" or "المشتريات" => SectionType.Purchases,
                    "inventory" or "مخزون" or "المخزون" => SectionType.Inventory,
                    "customers" or "عملاء" or "العملاء" => SectionType.Customers,
                    "suppliers" or "موردين" or "الموردين" => SectionType.Suppliers,
                    "accounting" or "محاسبة" or "المحاسبة" => SectionType.Accounting,
                    "reports" or "تقارير" or "التقارير" => SectionType.Reports,
                    "settings" or "إعدادات" or "الإعدادات" => SectionType.Settings,
                    _ => SectionType.Sales
                };

                return GetSectionColor(section);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على لون القسم من النص: {ex.Message}");
                return GetDefaultSectionColor(SectionType.Sales);
            }
        }

        /// <summary>
        /// تطبيق لون قسم على عنصر
        /// </summary>
        public static void ApplySectionColorToElement(FrameworkElement element, SectionType section)
        {
            try
            {
                var color = GetSectionColor(section);
                var brush = new SolidColorBrush(color);

                switch (element)
                {
                    case Button button:
                        button.Background = brush;
                        button.Foreground = GetContrastingTextBrush(color);
                        break;
                    case Border border:
                        border.Background = brush;
                        break;
                    case Panel panel:
                        panel.Background = brush;
                        break;
                    case Control control:
                        control.Background = brush;
                        control.Foreground = GetContrastingTextBrush(color);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق لون القسم على العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق لون قسم على عنصر من النص
        /// </summary>
        public static void ApplySectionColorToElement(FrameworkElement element, string sectionName)
        {
            try
            {
                var section = sectionName.ToLower() switch
                {
                    "sales" or "مبيعات" or "المبيعات" => SectionType.Sales,
                    "purchases" or "مشتريات" or "المشتريات" => SectionType.Purchases,
                    "inventory" or "مخزون" or "المخزون" => SectionType.Inventory,
                    "customers" or "عملاء" or "العملاء" => SectionType.Customers,
                    "suppliers" or "موردين" or "الموردين" => SectionType.Suppliers,
                    "accounting" or "محاسبة" or "المحاسبة" => SectionType.Accounting,
                    "reports" or "تقارير" or "التقارير" => SectionType.Reports,
                    "settings" or "إعدادات" or "الإعدادات" => SectionType.Settings,
                    _ => SectionType.Sales
                };

                ApplySectionColorToElement(element, section);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق لون القسم من النص: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على فرشاة نص متباينة
        /// </summary>
        private static SolidColorBrush GetContrastingTextBrush(Color backgroundColor)
        {
            try
            {
                // حساب سطوع اللون
                double brightness = (backgroundColor.R * 0.299 + backgroundColor.G * 0.587 + backgroundColor.B * 0.114) / 255;
                
                // إذا كان اللون فاتح، استخدم نص داكن، والعكس
                return brightness > 0.5 ? Brushes.Black : Brushes.White;
            }
            catch
            {
                return Brushes.White;
            }
        }

        /// <summary>
        /// إنشاء تدرج لوني للقسم
        /// </summary>
        public static LinearGradientBrush CreateSectionGradient(SectionType section, double opacity = 1.0)
        {
            try
            {
                var baseColor = GetSectionColor(section);
                var lightColor = GetLighterColor(baseColor, 0.3);
                var darkColor = GetDarkerColor(baseColor, 0.2);

                // تطبيق الشفافية
                lightColor.A = (byte)(opacity * 255 * 0.7);
                darkColor.A = (byte)(opacity * 255);

                var gradient = new LinearGradientBrush();
                gradient.StartPoint = new Point(0, 0);
                gradient.EndPoint = new Point(0, 1);
                gradient.GradientStops.Add(new GradientStop(lightColor, 0));
                gradient.GradientStops.Add(new GradientStop(darkColor, 1));

                return gradient;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء تدرج القسم: {ex.Message}");
                return new LinearGradientBrush(Colors.LightGray, Colors.Gray, 90);
            }
        }

        /// <summary>
        /// الحصول على لون أفتح
        /// </summary>
        private static Color GetLighterColor(Color color, double factor = 0.3)
        {
            return Color.FromArgb(
                color.A,
                (byte)Math.Min(255, color.R + (255 - color.R) * factor),
                (byte)Math.Min(255, color.G + (255 - color.G) * factor),
                (byte)Math.Min(255, color.B + (255 - color.B) * factor));
        }

        /// <summary>
        /// الحصول على لون أغمق
        /// </summary>
        private static Color GetDarkerColor(Color color, double factor = 0.3)
        {
            return Color.FromArgb(
                color.A,
                (byte)(color.R * (1 - factor)),
                (byte)(color.G * (1 - factor)),
                (byte)(color.B * (1 - factor)));
        }

        /// <summary>
        /// الحصول على جميع ألوان الأقسام
        /// </summary>
        public static Dictionary<SectionType, Color> GetAllSectionColors()
        {
            try
            {
                var colors = new Dictionary<SectionType, Color>();
                
                foreach (SectionType section in Enum.GetValues<SectionType>())
                {
                    colors[section] = GetSectionColor(section);
                }

                return colors;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على جميع ألوان الأقسام: {ex.Message}");
                return new Dictionary<SectionType, Color>();
            }
        }

        /// <summary>
        /// تحديث ألوان الأقسام في التطبيق
        /// </summary>
        public static void UpdateSectionColorsInApplication()
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources != null)
                {
                    foreach (SectionType section in Enum.GetValues<SectionType>())
                    {
                        var color = GetSectionColor(section);
                        var colorKey = $"{section}Color";
                        var brushKey = $"{section}Brush";
                        
                        app.Resources[colorKey] = color;
                        app.Resources[brushKey] = new SolidColorBrush(color);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث ألوان الأقسام: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان الأقسام على القائمة الجانبية
        /// </summary>
        public static void ApplySectionColorsToSidebar(Panel sidebarPanel)
        {
            try
            {
                if (sidebarPanel == null) return;

                foreach (var child in sidebarPanel.Children)
                {
                    if (child is Button button && button.Tag is string sectionName)
                    {
                        ApplySectionColorToElement(button, sectionName);
                    }
                    else if (child is Panel childPanel)
                    {
                        ApplySectionColorsToSidebar(childPanel);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان الأقسام على القائمة الجانبية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء أيقونة ملونة للقسم
        /// </summary>
        public static Border CreateSectionIcon(SectionType section, double size = 24)
        {
            try
            {
                var border = new Border
                {
                    Width = size,
                    Height = size,
                    CornerRadius = new CornerRadius(size / 4),
                    Background = GetSectionBrush(section),
                    BorderBrush = GetSectionBrush(section),
                    BorderThickness = new Thickness(1)
                };

                // إضافة نص أو رمز للقسم
                var textBlock = new TextBlock
                {
                    Text = GetSectionIcon(section),
                    FontSize = size * 0.6,
                    Foreground = GetContrastingTextBrush(GetSectionColor(section)),
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    FontWeight = FontWeights.Bold
                };

                border.Child = textBlock;
                return border;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء أيقونة القسم: {ex.Message}");
                return new Border { Width = size, Height = size, Background = Brushes.Gray };
            }
        }

        /// <summary>
        /// الحصول على رمز القسم
        /// </summary>
        private static string GetSectionIcon(SectionType section)
        {
            return section switch
            {
                SectionType.Sales => "💰",
                SectionType.Purchases => "🛒",
                SectionType.Inventory => "📦",
                SectionType.Customers => "👥",
                SectionType.Suppliers => "🏭",
                SectionType.Accounting => "📊",
                SectionType.Reports => "📈",
                SectionType.Settings => "⚙️",
                _ => "📋"
            };
        }

        /// <summary>
        /// الحصول على اسم القسم بالعربية
        /// </summary>
        public static string GetSectionNameInArabic(SectionType section)
        {
            return section switch
            {
                SectionType.Sales => "المبيعات",
                SectionType.Purchases => "المشتريات",
                SectionType.Inventory => "المخزون",
                SectionType.Customers => "العملاء",
                SectionType.Suppliers => "الموردين",
                SectionType.Accounting => "المحاسبة",
                SectionType.Reports => "التقارير",
                SectionType.Settings => "الإعدادات",
                _ => "غير محدد"
            };
        }

        /// <summary>
        /// الحصول على وصف القسم
        /// </summary>
        public static string GetSectionDescription(SectionType section)
        {
            return section switch
            {
                SectionType.Sales => "إدارة عمليات البيع والفواتير",
                SectionType.Purchases => "إدارة عمليات الشراء والموردين",
                SectionType.Inventory => "إدارة المخزون والمنتجات",
                SectionType.Customers => "إدارة بيانات العملاء",
                SectionType.Suppliers => "إدارة بيانات الموردين",
                SectionType.Accounting => "النظام المحاسبي والقيود",
                SectionType.Reports => "التقارير والإحصائيات",
                SectionType.Settings => "إعدادات النظام",
                _ => "قسم غير محدد"
            };
        }
    }
}