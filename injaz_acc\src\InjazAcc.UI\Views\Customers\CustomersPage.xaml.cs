using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using InjazAcc.UI.Views.Dialogs;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Customers
{
    /// <summary>
    /// Interaction logic for CustomersPage.xaml
    /// </summary>
    public partial class CustomersPage : Page
    {
        private ObservableCollection<Customer> _customers = new();
        private readonly InjazAcc.DataAccess.InjazAccDbContext _dbContext;

        public CustomersPage()
        {
            InitializeComponent();
            _dbContext = new InjazAcc.DataAccess.InjazAccDbContext();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للعملاء
                _customers = new ObservableCollection<Customer>
                {
                    new Customer { Id = 1, Code = "C001", Name = "شركة الأمل التجارية", Phone = "0501234567", Email = "<EMAIL>", Address = "الرياض", OpeningBalance = 5000 },
                    new Customer { Id = 2, Code = "C002", Name = "مؤسسة النور", Phone = "0507654321", Email = "<EMAIL>", Address = "جدة", OpeningBalance = 3000 },
                    new Customer { Id = 3, Code = "C003", Name = "شركة الإعمار", Phone = "0551234567", Email = "<EMAIL>", Address = "الدمام", OpeningBalance = 7500 },
                    new Customer { Id = 4, Code = "C004", Name = "مؤسسة الفجر", Phone = "0557654321", Email = "<EMAIL>", Address = "مكة", OpeningBalance = 2000 },
                    new Customer { Id = 5, Code = "C005", Name = "شركة البناء الحديث", Phone = "0561234567", Email = "<EMAIL>", Address = "المدينة", OpeningBalance = 4500 }
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات التجريبية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // ربط أزرار صفحة العملاء
        private void btnAddCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new InjazAcc.UI.Views.Dialogs.AddCustomerDialog();
                if (dialog.ShowDialog() == true && dialog.IsSuccess)
                {
                    MessageBox.Show("تم إضافة العميل بنجاح", "نجح الإضافة", MessageBoxButton.OK, MessageBoxImage.Information);
                    // TODO: تحديث قائمة العملاء
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة تعديل العميل المحدد", "تعديل عميل", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة تعديل العميل المحدد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteCustomer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف العميل المحدد؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("تم حذف العميل بنجاح", "حذف عميل", MessageBoxButton.OK, MessageBoxImage.Information);
                    // TODO: تنفيذ عملية الحذف
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تنفيذ وظيفة البحث عن العملاء", "البحث", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تنفيذ وظيفة البحث
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnRefresh_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadSampleData();
                MessageBox.Show("تم تحديث قائمة العملاء", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCustomerStatement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح كشف حساب العميل المحدد", "كشف حساب العميل", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح كشف حساب العميل
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCustomerPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة تسجيل دفعة من العميل", "دفعة من عميل", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة تسجيل دفعة من العميل
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportCustomers_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تصدير قائمة العملاء إلى ملف Excel", "تصدير العملاء", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تنفيذ تصدير العملاء
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintCustomers_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم طباعة قائمة العملاء", "طباعة العملاء", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تنفيذ طباعة العملاء
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCustomerReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح تقرير العملاء", "تقرير العملاء", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح تقرير العملاء
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تصدير قائمة العملاء إلى Excel", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgCustomers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgCustomers.SelectedItem != null)
                {
                    MessageBox.Show("سيتم فتح تفاصيل العميل", "تفاصيل العميل", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCustomerDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح تفاصيل العميل المحدد", "تفاصيل العميل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
