using System;
using System.Configuration;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using InjazAcc.Core.Interfaces;
using InjazAcc.DataAccess;
using InjazAcc.Services;
using InjazAcc.UI.Helpers;
using InjazAcc.UI.Services;
using OfficeOpenXml;

namespace InjazAcc.UI;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    // حاوية الاعتمادية
    private static IServiceProvider ServiceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // إعداد الترخيص الجديد لـ EPPlus
        // ExcelPackage.License = new ExcelLicenseProvider("YourLicenseKeyHere");

        // تعيين ترخيص EPPlus - الحل النهائي
        try
        {
            // استخدام Reflection للوصول للطريقة الصحيحة
            var licenseProperty = typeof(ExcelPackage).GetProperty("License", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
            if (licenseProperty != null && licenseProperty.CanWrite)
            {
                var licenseType = licenseProperty.PropertyType;
                var nonCommercialValue = Enum.Parse(licenseType, "NonCommercial");
                licenseProperty.SetValue(null, nonCommercialValue);
            }
            else
            {
                // الطريقة القديمة - تم إزالتها لتجنب التحذيرات
            }
        }
        catch
        {
            // تم إزالة الطريقة القديمة لتجنب التحذيرات
        }

        // تعيين ترخيص EPPlus في بداية التطبيق
        try
        {
            // استخدام Reflection للوصول للخاصية الجديدة
            var excelPackageType = typeof(ExcelPackage);
            var licenseProperty = excelPackageType.GetProperty("License", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);

            if (licenseProperty != null)
            {
                // EPPlus 8+ - استخدم ExcelPackage.License
                var licenseTypeEnum = excelPackageType.Assembly.GetType("OfficeOpenXml.LicenseType");
                if (licenseTypeEnum != null)
                {
                    var nonCommercialValue = Enum.Parse(licenseTypeEnum, "NonCommercial");
                    licenseProperty.SetValue(null, nonCommercialValue);
                }
            }
            else
            {
                // EPPlus 7 وما قبل - تم إزالة الطريقة القديمة لتجنب التحذيرات
            }
        }
        catch (Exception ex)
        {
            // تجاهل الأخطاء نهائياً - التطبيق سيعمل
            System.Diagnostics.Debug.WriteLine($"EPPlus License setup failed: {ex.Message}");
        }

        // تهيئة حاوية الاعتمادية
        ConfigureServices();

        // تهيئة قاعدة البيانات
        InitializeDatabaseAsync();

        // تحميل وتطبيق إعدادات الألوان
        ThemeManager.LoadSettings();
        ThemeManager.ApplyTheme();

        // تحميل وتطبيق إعدادات الألوان المتقدمة
        AdvancedColorManager.LoadSettings();
        AdvancedColorManager.ApplyAllColors();

        // إضافة معالج للاستثناءات غير المعالجة
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
        this.DispatcherUnhandledException += App_DispatcherUnhandledException;
    }

    /// <summary>
    /// تهيئة حاوية الاعتمادية وتسجيل الخدمات
    /// </summary>
    private void ConfigureServices()
    {
        var services = new ServiceCollection();

        // تسجيل وحدة العمل وسياق قاعدة البيانات
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddDbContext<InjazAccDbContext>();

        // تسجيل خدمات التطبيق
        services.AddApplicationServices();

        // بناء حاوية الاعتمادية
        ServiceProvider = services.BuildServiceProvider();
    }

    /// <summary>
    /// تهيئة قاعدة البيانات بشكل غير متزامن
    /// </summary>
    private async void InitializeDatabaseAsync()
    {
        try
        {
            await DatabaseService.InitializeDatabaseAsync();
        }
        catch (Exception ex)
        {
            // في حالة فشل تهيئة قاعدة البيانات، سجل الخطأ ولكن لا توقف التطبيق
            LogError($"فشل في تهيئة قاعدة البيانات: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على خدمة من حاوية الاعتمادية
    /// </summary>
    public static T GetService<T>()
    {
        return ServiceProvider.GetService<T>();
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            Exception ex = (Exception)e.ExceptionObject;
            string errorMessage = $"حدث خطأ غير متوقع: {ex.Message}\n\nStack Trace:\n{ex.StackTrace}";

            // كتابة الخطأ في ملف سجل الأخطاء
            LogError(errorMessage);

            MessageBox.Show(errorMessage, "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ الأصلي
            MessageBox.Show("حدث خطأ غير متوقع في التطبيق.", "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        try
        {
            string errorMessage = $"حدث خطأ غير متوقع: {e.Exception.Message}\n\nStack Trace:\n{e.Exception.StackTrace}";

            // كتابة الخطأ في ملف سجل الأخطاء
            LogError(errorMessage);

            MessageBox.Show(errorMessage, "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);

            // تعليم الاستثناء كمعالج
            e.Handled = true;
        }
        catch
        {
            // في حالة حدوث خطأ أثناء معالجة الخطأ الأصلي
            MessageBox.Show("حدث خطأ غير متوقع في التطبيق.", "خطأ غير متوقع", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LogError(string errorMessage)
    {
        try
        {
            string logFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");

            // إنشاء مجلد السجلات إذا لم يكن موجوداً
            if (!Directory.Exists(logFolder))
            {
                Directory.CreateDirectory(logFolder);
            }

            string logFile = Path.Combine(logFolder, $"ErrorLog_{DateTime.Now:yyyy-MM-dd}.txt");
            string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {errorMessage}\n\n";

            // كتابة الخطأ في ملف السجل
            File.AppendAllText(logFile, logEntry);
        }
        catch
        {
            // تجاهل أي خطأ أثناء كتابة السجل
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        try
        {
            using (var dbContext = new InjazAcc.DataAccess.InjazAccDbContext())
            {
                dbContext.SaveChanges();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء حفظ البيانات قبل الخروج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        base.OnExit(e);
    }
}

