using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Media;

namespace InjazAcc.UI.Helpers
{
    /// <summary>
    /// مدير الألوان المتقدم للتحكم الشامل بجميع ألوان التطبيق
    /// </summary>
    public static class AdvancedColorManager
    {
        private static readonly string SettingsPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "InjazAcc", "ColorSettings.json");

        private static AdvancedColorSettings _currentSettings = new AdvancedColorSettings();

        /// <summary>
        /// إعدادات الألوان المتقدمة
        /// </summary>
        public class AdvancedColorSettings
        {
            // الألوان الأساسية
            public string PrimaryColor { get; set; } = "#009688";
            public string PrimaryDarkColor { get; set; } = "#00695C";
            public string PrimaryLightColor { get; set; } = "#4DB6AC";
            
            public string SecondaryColor { get; set; } = "#FFC107";
            public string SecondaryDarkColor { get; set; } = "#FF8F00";
            public string SecondaryLightColor { get; set; } = "#FFECB3";
            
            public string AccentColor { get; set; } = "#FF4081";
            
            // ألوان الخلفية
            public string BackgroundColor { get; set; } = "#FAFAFA";
            public string SurfaceColor { get; set; } = "#FFFFFF";
            public string CardBackgroundColor { get; set; } = "#FFFFFF";
            public string DialogBackgroundColor { get; set; } = "#FFFFFF";
            public string SidebarBackgroundColor { get; set; } = "#263238";
            public string HeaderBackgroundColor { get; set; } = "#FFFFFF";
            public string FooterBackgroundColor { get; set; } = "#F5F5F5";
            
            // ألوان النصوص
            public string PrimaryTextColor { get; set; } = "#212121";
            public string SecondaryTextColor { get; set; } = "#757575";
            public string DisabledTextColor { get; set; } = "#BDBDBD";
            public string HintTextColor { get; set; } = "#9E9E9E";
            public string OnPrimaryTextColor { get; set; } = "#FFFFFF";
            public string OnSecondaryTextColor { get; set; } = "#000000";
            public string OnSurfaceTextColor { get; set; } = "#000000";
            public string OnBackgroundTextColor { get; set; } = "#000000";
            
            // ألوان الحدود
            public string BorderColor { get; set; } = "#E0E0E0";
            public string DividerColor { get; set; } = "#EEEEEE";
            public string OutlineColor { get; set; } = "#BDBDBD";
            public string FocusBorderColor { get; set; } = "#2196F3";
            public string ErrorBorderColor { get; set; } = "#F44336";
            
            // ألوان الأزرار
            public string ButtonBackgroundColor { get; set; } = "#009688";
            public string ButtonTextColor { get; set; } = "#FFFFFF";
            public string ButtonHoverColor { get; set; } = "#00796B";
            public string ButtonPressedColor { get; set; } = "#004D40";
            public string ButtonDisabledColor { get; set; } = "#E0E0E0";
            public string ButtonDisabledTextColor { get; set; } = "#9E9E9E";
            
            // ألوان الأزرار الثانوية
            public string SecondaryButtonBackgroundColor { get; set; } = "#F5F5F5";
            public string SecondaryButtonTextColor { get; set; } = "#424242";
            public string SecondaryButtonHoverColor { get; set; } = "#EEEEEE";
            public string SecondaryButtonPressedColor { get; set; } = "#E0E0E0";
            
            // ألوان التحديد والتركيز
            public string SelectionColor { get; set; } = "#E3F2FD";
            public string HoverColor { get; set; } = "#F5F5F5";
            public string FocusColor { get; set; } = "#2196F3";
            public string ActiveColor { get; set; } = "#1976D2";
            public string HighlightColor { get; set; } = "#FFEB3B";
            
            // ألوان الحالة
            public string SuccessColor { get; set; } = "#4CAF50";
            public string SuccessLightColor { get; set; } = "#C8E6C9";
            public string SuccessDarkColor { get; set; } = "#2E7D32";
            
            public string WarningColor { get; set; } = "#FF9800";
            public string WarningLightColor { get; set; } = "#FFE0B2";
            public string WarningDarkColor { get; set; } = "#F57C00";
            
            public string ErrorColor { get; set; } = "#F44336";
            public string ErrorLightColor { get; set; } = "#FFCDD2";
            public string ErrorDarkColor { get; set; } = "#D32F2F";
            
            public string InfoColor { get; set; } = "#2196F3";
            public string InfoLightColor { get; set; } = "#BBDEFB";
            public string InfoDarkColor { get; set; } = "#1976D2";
            
            // ألوان الجداول
            public string TableHeaderColor { get; set; } = "#F5F5F5";
            public string TableRowEvenColor { get; set; } = "#FAFAFA";
            public string TableRowOddColor { get; set; } = "#FFFFFF";
            public string TableBorderColor { get; set; } = "#E0E0E0";
            public string TableSelectedRowColor { get; set; } = "#E3F2FD";
            public string TableHoverRowColor { get; set; } = "#F5F5F5";
            
            // ألوان الرسوم البيانية
            public string Chart1Color { get; set; } = "#2196F3";
            public string Chart2Color { get; set; } = "#4CAF50";
            public string Chart3Color { get; set; } = "#FF9800";
            public string Chart4Color { get; set; } = "#9C27B0";
            public string Chart5Color { get; set; } = "#F44336";
            public string Chart6Color { get; set; } = "#00BCD4";
            public string Chart7Color { get; set; } = "#8BC34A";
            public string Chart8Color { get; set; } = "#FFC107";
            
            // ألوان الأقسام
            public string SalesColor { get; set; } = "#4CAF50";
            public string PurchasesColor { get; set; } = "#FF9800";
            public string InventoryColor { get; set; } = "#2196F3";
            public string CustomersColor { get; set; } = "#9C27B0";
            public string SuppliersColor { get; set; } = "#FF5722";
            public string AccountingColor { get; set; } = "#795548";
            public string ReportsColor { get; set; } = "#607D8B";
            public string SettingsColor { get; set; } = "#9E9E9E";
            
            // إعدادات إضافية
            public string BaseTheme { get; set; } = "Light"; // Light, Dark
            public bool UseSystemAccentColor { get; set; } = false;
            public bool HighContrastMode { get; set; } = false;
            public double OpacityLevel { get; set; } = 1.0;
        }

        /// <summary>
        /// الحصول على الإعدادات الحالية
        /// </summary>
        public static AdvancedColorSettings CurrentSettings => _currentSettings;

        /// <summary>
        /// تحميل إعدادات الألوان
        /// </summary>
        public static void LoadSettings()
        {
            try
            {
                if (File.Exists(SettingsPath))
                {
                    var json = File.ReadAllText(SettingsPath);
                    _currentSettings = JsonSerializer.Deserialize<AdvancedColorSettings>(json) ?? new AdvancedColorSettings();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل إعدادات الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// حفظ إعدادات الألوان
        /// </summary>
        public static void SaveSettings()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                var json = JsonSerializer.Serialize(_currentSettings, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                File.WriteAllText(SettingsPath, json);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ إعدادات الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// تطبيق جميع الألوان على التطبيق
        /// </summary>
        public static void ApplyAllColors()
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources == null) return;

                // تطبيق الألوان الأساسية
                ApplyColor("PrimaryColor", _currentSettings.PrimaryColor);
                ApplyColor("PrimaryDarkColor", _currentSettings.PrimaryDarkColor);
                ApplyColor("PrimaryLightColor", _currentSettings.PrimaryLightColor);
                
                ApplyColor("SecondaryColor", _currentSettings.SecondaryColor);
                ApplyColor("SecondaryDarkColor", _currentSettings.SecondaryDarkColor);
                ApplyColor("SecondaryLightColor", _currentSettings.SecondaryLightColor);
                
                ApplyColor("AccentColor", _currentSettings.AccentColor);
                
                // تطبيق ألوان الخلفية
                ApplyColor("BackgroundColor", _currentSettings.BackgroundColor);
                ApplyColor("SurfaceColor", _currentSettings.SurfaceColor);
                ApplyColor("CardBackgroundColor", _currentSettings.CardBackgroundColor);
                ApplyColor("DialogBackgroundColor", _currentSettings.DialogBackgroundColor);
                ApplyColor("SidebarBackgroundColor", _currentSettings.SidebarBackgroundColor);
                ApplyColor("HeaderBackgroundColor", _currentSettings.HeaderBackgroundColor);
                ApplyColor("FooterBackgroundColor", _currentSettings.FooterBackgroundColor);
                
                // تطبيق ألوان النصوص
                ApplyColor("PrimaryTextColor", _currentSettings.PrimaryTextColor);
                ApplyColor("SecondaryTextColor", _currentSettings.SecondaryTextColor);
                ApplyColor("DisabledTextColor", _currentSettings.DisabledTextColor);
                ApplyColor("HintTextColor", _currentSettings.HintTextColor);
                ApplyColor("OnPrimaryTextColor", _currentSettings.OnPrimaryTextColor);
                ApplyColor("OnSecondaryTextColor", _currentSettings.OnSecondaryTextColor);
                ApplyColor("OnSurfaceTextColor", _currentSettings.OnSurfaceTextColor);
                ApplyColor("OnBackgroundTextColor", _currentSettings.OnBackgroundTextColor);
                
                // تطبيق ألوان الحدود
                ApplyColor("BorderColor", _currentSettings.BorderColor);
                ApplyColor("DividerColor", _currentSettings.DividerColor);
                ApplyColor("OutlineColor", _currentSettings.OutlineColor);
                ApplyColor("FocusBorderColor", _currentSettings.FocusBorderColor);
                ApplyColor("ErrorBorderColor", _currentSettings.ErrorBorderColor);
                
                // تطبيق ألوان الأزرار
                ApplyColor("ButtonBackgroundColor", _currentSettings.ButtonBackgroundColor);
                ApplyColor("ButtonTextColor", _currentSettings.ButtonTextColor);
                ApplyColor("ButtonHoverColor", _currentSettings.ButtonHoverColor);
                ApplyColor("ButtonPressedColor", _currentSettings.ButtonPressedColor);
                ApplyColor("ButtonDisabledColor", _currentSettings.ButtonDisabledColor);
                ApplyColor("ButtonDisabledTextColor", _currentSettings.ButtonDisabledTextColor);
                
                ApplyColor("SecondaryButtonBackgroundColor", _currentSettings.SecondaryButtonBackgroundColor);
                ApplyColor("SecondaryButtonTextColor", _currentSettings.SecondaryButtonTextColor);
                ApplyColor("SecondaryButtonHoverColor", _currentSettings.SecondaryButtonHoverColor);
                ApplyColor("SecondaryButtonPressedColor", _currentSettings.SecondaryButtonPressedColor);
                
                // تطبيق ألوان التحديد والتركيز
                ApplyColor("SelectionColor", _currentSettings.SelectionColor);
                ApplyColor("HoverColor", _currentSettings.HoverColor);
                ApplyColor("FocusColor", _currentSettings.FocusColor);
                ApplyColor("ActiveColor", _currentSettings.ActiveColor);
                ApplyColor("HighlightColor", _currentSettings.HighlightColor);
                
                // تطبيق ألوان الحالة
                ApplyColor("SuccessColor", _currentSettings.SuccessColor);
                ApplyColor("SuccessLightColor", _currentSettings.SuccessLightColor);
                ApplyColor("SuccessDarkColor", _currentSettings.SuccessDarkColor);
                
                ApplyColor("WarningColor", _currentSettings.WarningColor);
                ApplyColor("WarningLightColor", _currentSettings.WarningLightColor);
                ApplyColor("WarningDarkColor", _currentSettings.WarningDarkColor);
                
                ApplyColor("ErrorColor", _currentSettings.ErrorColor);
                ApplyColor("ErrorLightColor", _currentSettings.ErrorLightColor);
                ApplyColor("ErrorDarkColor", _currentSettings.ErrorDarkColor);
                
                ApplyColor("InfoColor", _currentSettings.InfoColor);
                ApplyColor("InfoLightColor", _currentSettings.InfoLightColor);
                ApplyColor("InfoDarkColor", _currentSettings.InfoDarkColor);
                
                // تطبيق ألوان الجداول
                ApplyColor("TableHeaderColor", _currentSettings.TableHeaderColor);
                ApplyColor("TableRowEvenColor", _currentSettings.TableRowEvenColor);
                ApplyColor("TableRowOddColor", _currentSettings.TableRowOddColor);
                ApplyColor("TableBorderColor", _currentSettings.TableBorderColor);
                ApplyColor("TableSelectedRowColor", _currentSettings.TableSelectedRowColor);
                ApplyColor("TableHoverRowColor", _currentSettings.TableHoverRowColor);
                
                // تطبيق ألوان الرسوم البيانية
                ApplyColor("Chart1Color", _currentSettings.Chart1Color);
                ApplyColor("Chart2Color", _currentSettings.Chart2Color);
                ApplyColor("Chart3Color", _currentSettings.Chart3Color);
                ApplyColor("Chart4Color", _currentSettings.Chart4Color);
                ApplyColor("Chart5Color", _currentSettings.Chart5Color);
                ApplyColor("Chart6Color", _currentSettings.Chart6Color);
                ApplyColor("Chart7Color", _currentSettings.Chart7Color);
                ApplyColor("Chart8Color", _currentSettings.Chart8Color);
                
                // تطبيق ألوان الأقسام
                ApplyColor("SalesColor", _currentSettings.SalesColor);
                ApplyColor("PurchasesColor", _currentSettings.PurchasesColor);
                ApplyColor("InventoryColor", _currentSettings.InventoryColor);
                ApplyColor("CustomersColor", _currentSettings.CustomersColor);
                ApplyColor("SuppliersColor", _currentSettings.SuppliersColor);
                ApplyColor("AccountingColor", _currentSettings.AccountingColor);
                ApplyColor("ReportsColor", _currentSettings.ReportsColor);
                ApplyColor("SettingsColor", _currentSettings.SettingsColor);
                
                // تطبيق الألوان على جميع عناصر الواجهة
                UIColorApplier.ApplyColorsToAllWindows();
                
                // تحديث ألوان الرسوم البيانية
                ChartColorManager.UpdateChartColorsInApplication();
                
                // تحديث ألوان الأقسام
                SectionColorManager.UpdateSectionColorsInApplication();
                
                // حفظ الإعدادات
                SaveSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// تطبيق لون واحد
        /// </summary>
        private static void ApplyColor(string colorKey, string colorValue)
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources == null) return;

                var color = (Color)ColorConverter.ConvertFromString(colorValue);
                
                // تحديث اللون
                app.Resources[colorKey] = color;
                
                // تحديث الفرشاة المقابلة
                var brushKey = colorKey.Replace("Color", "Brush");
                app.Resources[brushKey] = new SolidColorBrush(color);
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء الفردية لتجنب توقف التطبيق
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق اللون {colorKey}: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث لون معين
        /// </summary>
        public static void UpdateColor(string colorProperty, string newColor)
        {
            try
            {
                var property = typeof(AdvancedColorSettings).GetProperty(colorProperty);
                if (property != null && property.CanWrite)
                {
                    property.SetValue(_currentSettings, newColor);
                    ApplyColor(colorProperty, newColor);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث اللون: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// الحصول على قائمة بجميع الألوان
        /// </summary>
        public static Dictionary<string, string> GetAllColors()
        {
            var colors = new Dictionary<string, string>();
            var properties = typeof(AdvancedColorSettings).GetProperties();
            
            foreach (var property in properties)
            {
                if (property.PropertyType == typeof(string) && property.Name.Contains("Color"))
                {
                    var value = property.GetValue(_currentSettings)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        colors[property.Name] = value;
                    }
                }
            }
            
            return colors;
        }

        /// <summary>
        /// إعادة تعيين الألوان إلى القيم الافتراضية
        /// </summary>
        public static void ResetToDefaults()
        {
            _currentSettings = new AdvancedColorSettings();
            ApplyAllColors();
        }

        /// <summary>
        /// تطبيق قالب ألوان جاهز
        /// </summary>
        public static void ApplyColorTheme(string themeName)
        {
            switch (themeName.ToLower())
            {
                case "default":
                    ResetToDefaults();
                    break;
                    
                case "dark":
                    ApplyDarkTheme();
                    break;
                    
                case "blue":
                    ApplyBlueTheme();
                    break;
                    
                case "green":
                    ApplyGreenTheme();
                    break;
                    
                case "purple":
                    ApplyPurpleTheme();
                    break;
                    
                case "orange":
                    ApplyOrangeTheme();
                    break;
                    
                case "red":
                    ApplyRedTheme();
                    break;
                    
                case "highcontrast":
                    ApplyHighContrastTheme();
                    break;
            }
        }

        private static void ApplyDarkTheme()
        {
            _currentSettings.BaseTheme = "Dark";
            _currentSettings.BackgroundColor = "#121212";
            _currentSettings.SurfaceColor = "#1E1E1E";
            _currentSettings.CardBackgroundColor = "#2D2D2D";
            _currentSettings.PrimaryTextColor = "#FFFFFF";
            _currentSettings.SecondaryTextColor = "#B3B3B3";
            ApplyAllColors();
        }

        private static void ApplyBlueTheme()
        {
            _currentSettings.PrimaryColor = "#2196F3";
            _currentSettings.SecondaryColor = "#03DAC6";
            _currentSettings.AccentColor = "#FF4081";
            ApplyAllColors();
        }

        private static void ApplyGreenTheme()
        {
            _currentSettings.PrimaryColor = "#4CAF50";
            _currentSettings.SecondaryColor = "#8BC34A";
            _currentSettings.AccentColor = "#FF9800";
            ApplyAllColors();
        }

        private static void ApplyPurpleTheme()
        {
            _currentSettings.PrimaryColor = "#9C27B0";
            _currentSettings.SecondaryColor = "#E91E63";
            _currentSettings.AccentColor = "#FFC107";
            ApplyAllColors();
        }

        private static void ApplyOrangeTheme()
        {
            _currentSettings.PrimaryColor = "#FF9800";
            _currentSettings.SecondaryColor = "#FF5722";
            _currentSettings.AccentColor = "#4CAF50";
            ApplyAllColors();
        }

        private static void ApplyRedTheme()
        {
            _currentSettings.PrimaryColor = "#F44336";
            _currentSettings.SecondaryColor = "#E91E63";
            _currentSettings.AccentColor = "#2196F3";
            ApplyAllColors();
        }

        private static void ApplyHighContrastTheme()
        {
            _currentSettings.HighContrastMode = true;
            _currentSettings.BackgroundColor = "#FFFFFF";
            _currentSettings.PrimaryTextColor = "#000000";
            _currentSettings.BorderColor = "#000000";
            _currentSettings.ButtonBackgroundColor = "#000000";
            _currentSettings.ButtonTextColor = "#FFFFFF";
            ApplyAllColors();
        }
    }
}