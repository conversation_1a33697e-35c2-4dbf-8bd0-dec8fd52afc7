using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Reports
{
    /// <summary>
    /// Interaction logic for ReportsPage.xaml
    /// </summary>
    public partial class ReportsPage : Page
    {
        public ReportsPage()
        {
            try
            {
                InitializeComponent();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تهيئة صفحة التقارير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SalesReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (NavigationService != null)
                {
                    Page salesReportsPage = new SalesReportsPage();
                    NavigationService.Navigate(salesReportsPage);
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة تقارير المبيعات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة تقارير المبيعات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PurchasesReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (NavigationService != null)
                {
                    Page purchasesReportsPage = new PurchasesReportsPage();
                    NavigationService.Navigate(purchasesReportsPage);
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة تقارير المشتريات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة تقارير المشتريات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InventoryReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (NavigationService != null)
                {
                    Page inventoryReportsPage = new InventoryReportsPage();
                    NavigationService.Navigate(inventoryReportsPage);
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة تقارير المخزون", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة تقارير المخزون: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CustomersAndSuppliersReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (NavigationService != null)
                {
                    Page customersAndSuppliersReportsPage = new CustomersAndSuppliersReportsPage();
                    NavigationService.Navigate(customersAndSuppliersReportsPage);
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة تقارير العملاء والموردين", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة تقارير العملاء والموردين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FinancialReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (NavigationService != null)
                {
                    // تستخدم مسار نسبي للوصول إلى صفحة التقارير المالية
                    Type financialReportsPageType = Type.GetType("InjazAcc.UI.Views.Accounts.FinancialReportsPage, InjazAcc.UI");
                    if (financialReportsPageType != null)
                    {
                        Page financialReportsPage = (Page)Activator.CreateInstance(financialReportsPageType);
                        NavigationService.Navigate(financialReportsPage);
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن العثور على نوع صفحة التقارير المالية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show("لا يمكن الانتقال إلى صفحة التقارير المالية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة التقارير المالية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CustomReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                MessageBox.Show("تقارير مخصصة قيد التطوير", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackToMain_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى الصفحة الرئيسية
                if (NavigationService != null)
                {
                    // استخدام الـ NavigationService للعودة للصفحة الرئيسية
                    // يمكن استخدام GoBack() إذا كانت الصفحة الرئيسية هي الصفحة السابقة
                    // أو يمكن إنشاء صفحة جديدة والانتقال إليها
                    NavigationService.GoBack();
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى الصفحة الرئيسية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة للصفحة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أزرار إضافية لصفحة التقارير
        private void btnDailyReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح التقرير اليومي", "التقرير اليومي", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح التقرير اليومي
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnWeeklyReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح التقرير الأسبوعي", "التقرير الأسبوعي", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح التقرير الأسبوعي
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnMonthlyReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح التقرير الشهري", "التقرير الشهري", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح التقرير الشهري
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnYearlyReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح التقرير السنوي", "التقرير السنوي", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح التقرير السنوي
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم طباعة التقرير المحدد", "طباعة التقرير", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: طباعة التقرير المحدد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تصدير التقرير إلى ملف Excel", "تصدير التقرير", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تصدير التقرير إلى Excel
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEmailReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إرسال التقرير عبر البريد الإلكتروني", "إرسال التقرير", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إرسال التقرير عبر البريد الإلكتروني
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnReportSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح إعدادات التقارير", "إعدادات التقارير", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح إعدادات التقارير
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnRefreshReports_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("تم تحديث قائمة التقارير", "تحديث التقارير", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تحديث قائمة التقارير
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
