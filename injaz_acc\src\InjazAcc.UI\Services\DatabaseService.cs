using InjazAcc.DataAccess;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using System.Windows;

namespace InjazAcc.UI.Services
{
    /// <summary>
    /// خدمة إدارة قاعدة البيانات في التطبيق
    /// </summary>
    public static class DatabaseService
    {
        /// <summary>
        /// تهيئة قاعدة البيانات عند بدء التطبيق
        /// </summary>
        public static async Task InitializeDatabaseAsync()
        {
            try
            {
                using var context = new InjazAccDbContext();
                
                // تهيئة قاعدة البيانات مع البيانات الأولية
                await DatabaseInitializer.InitializeAsync(context);
                
                Console.WriteLine("✅ تم تهيئة قاعدة البيانات بنجاح");
            }
            catch (Exception ex)
            {
                var errorMessage = $"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}";
                Console.WriteLine(errorMessage);
                
                // عرض رسالة خطأ للمستخدم
                MessageBox.Show(
                    $"حدث خطأ أثناء تهيئة قاعدة البيانات:\n\n{ex.Message}\n\nتأكد من:\n" +
                    "1. تثبيت SQL Server LocalDB\n" +
                    "2. صحة سلسلة الاتصال\n" +
                    "3. وجود صلاحيات الكتابة",
                    "خطأ في قاعدة البيانات",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                
                throw;
            }
        }

        /// <summary>
        /// التحقق من اتصال قاعدة البيانات
        /// </summary>
        public static async Task<bool> TestConnectionAsync()
        {
            try
            {
                using var context = new InjazAccDbContext();
                await context.Database.CanConnectAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        public static async Task<bool> CreateBackupAsync(string backupPath)
        {
            try
            {
                using var context = new InjazAccDbContext();
                
                // في بيئة الإنتاج، يمكن استخدام SQL Server backup commands
                // هنا نستخدم طريقة بسيطة للتطوير
                
                var connectionString = context.Database.GetConnectionString();
                
                // يمكن تنفيذ backup script هنا
                // await context.Database.ExecuteSqlRawAsync($"BACKUP DATABASE...");
                
                Console.WriteLine($"✅ تم إنشاء نسخة احتياطية في: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// استعادة قاعدة البيانات من نسخة احتياطية
        /// </summary>
        public static async Task<bool> RestoreBackupAsync(string backupPath)
        {
            try
            {
                using var context = new InjazAccDbContext();
                
                // في بيئة الإنتاج، يمكن استخدام SQL Server restore commands
                // هنا نستخدم طريقة بسيطة للتطوير
                
                // يمكن تنفيذ restore script هنا
                // await context.Database.ExecuteSqlRawAsync($"RESTORE DATABASE...");
                
                Console.WriteLine($"✅ تم استعادة النسخة الاحتياطية من: {backupPath}");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في استعادة النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعادة تعيين قاعدة البيانات (حذف وإعادة إنشاء)
        /// </summary>
        public static async Task<bool> ResetDatabaseAsync()
        {
            try
            {
                var result = MessageBox.Show(
                    "هل أنت متأكد من إعادة تعيين قاعدة البيانات؟\n\nسيتم حذف جميع البيانات الموجودة!",
                    "تأكيد إعادة التعيين",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning
                );

                if (result != MessageBoxResult.Yes)
                    return false;

                using var context = new InjazAccDbContext();
                
                // حذف قاعدة البيانات
                await context.Database.EnsureDeletedAsync();
                
                // إعادة إنشاء قاعدة البيانات مع البيانات الأولية
                await DatabaseInitializer.InitializeAsync(context);
                
                MessageBox.Show(
                    "تم إعادة تعيين قاعدة البيانات بنجاح!",
                    "نجح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"خطأ في إعادة تعيين قاعدة البيانات:\n{ex.Message}",
                    "خطأ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error
                );
                
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات قاعدة البيانات
        /// </summary>
        public static async Task<string> GetDatabaseInfoAsync()
        {
            try
            {
                using var context = new InjazAccDbContext();
                
                var connectionString = context.Database.GetConnectionString();
                var canConnect = await context.Database.CanConnectAsync();
                
                var info = $"سلسلة الاتصال: {connectionString}\n";
                info += $"حالة الاتصال: {(canConnect ? "متصل" : "غير متصل")}\n";
                
                if (canConnect)
                {
                    var usersCount = await context.Users.CountAsync();
                    var customersCount = await context.Customers.CountAsync();
                    var productsCount = await context.Products.CountAsync();
                    var invoicesCount = await context.Invoices.CountAsync();
                    
                    info += $"عدد المستخدمين: {usersCount}\n";
                    info += $"عدد العملاء: {customersCount}\n";
                    info += $"عدد المنتجات: {productsCount}\n";
                    info += $"عدد الفواتير: {invoicesCount}\n";
                }
                
                return info;
            }
            catch (Exception ex)
            {
                return $"خطأ في الحصول على معلومات قاعدة البيانات: {ex.Message}";
            }
        }
    }
}
