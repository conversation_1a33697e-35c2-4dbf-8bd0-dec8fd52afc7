using System;
using System.Collections.Generic;
using System.IO;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;

class Program
{
    static void Main()
    {
        try
        {
            // تعيين الترخيص لـ EPPlus
            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
            Console.WriteLine("🔧 تم تعيين ترخيص EPPlus...");

        // بيانات تجريبية للعملاء
        var customers = new List<Customer>
        {
            new Customer { Code = "C001", Name = "شركة الأهرام للتجارة", ContactPerson = "أحمد محمد", Phone = "01234567890", Email = "<EMAIL>", Address = "القاهرة", CreditLimit = 50000, OpeningBalance = 25000 },
            new Customer { Code = "C002", Name = "مؤسسة النور للمقاولات", ContactPerson = "فاطمة أحمد", Phone = "01098765432", Email = "<EMAIL>", Address = "الإسكندرية", CreditLimit = 75000, OpeningBalance = 45000 },
            new Customer { Code = "C003", Name = "شركة الفجر للصناعات", ContactPerson = "محمد حسن", Phone = "01555123456", Email = "<EMAIL>", Address = "الجيزة", CreditLimit = 100000, OpeningBalance = 80000 }
        };

            Console.WriteLine("📊 إنشاء بيانات العملاء...");

            using (var package = new ExcelPackage())
            {
                Console.WriteLine("📝 إنشاء ورقة العمل...");
                var worksheet = package.Workbook.Worksheets.Add("تقرير العملاء");

                Console.WriteLine("🎨 تطبيق التصميم الجميل...");
                ApplyBeautifulDesign(worksheet, customers);

                Console.WriteLine("💾 حفظ الملف...");
                var fileInfo = new FileInfo("تقرير_العملاء_EPPlus.xlsx");
                package.SaveAs(fileInfo);

                Console.WriteLine($"📁 تم حفظ الملف في: {fileInfo.FullName}");
            }

            Console.WriteLine("✅ تم إنشاء ملف Excel بنجاح باستخدام EPPlus!");
            Console.WriteLine("📁 اسم الملف: تقرير_العملاء_EPPlus.xlsx");
            Console.WriteLine("🎨 التصميم يحتوي على نفس الألوان والتنسيقات الجميلة!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ حدث خطأ: {ex.Message}");
            Console.WriteLine($"📋 تفاصيل الخطأ: {ex}");
        }
    }

    static void ApplyBeautifulDesign(ExcelWorksheet worksheet, List<Customer> customers)
    {
        // تعيين عرض الأعمدة
        worksheet.Column(1).Width = 15;  // كود العميل
        worksheet.Column(2).Width = 25;  // اسم العميل
        worksheet.Column(3).Width = 20;  // الشخص المسؤول
        worksheet.Column(4).Width = 18;  // رقم الهاتف
        worksheet.Column(5).Width = 30;  // البريد الإلكتروني
        worksheet.Column(6).Width = 25;  // العنوان
        worksheet.Column(7).Width = 18;  // حد الائتمان
        worksheet.Column(8).Width = 18;  // الرصيد الحالي

        // العنوان الرئيسي
        worksheet.Cells[1, 1, 1, 8].Merge = true;
        worksheet.Cells[1, 1].Value = "تقرير العملاء - نظام إنجاز المحاسبي";
        worksheet.Cells[1, 1].Style.Font.Size = 16;
        worksheet.Cells[1, 1].Style.Font.Bold = true;
        worksheet.Cells[1, 1].Style.Font.Color.SetColor(Color.FromArgb(139, 69, 19)); // #8B4513
        worksheet.Cells[1, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
        worksheet.Cells[1, 1].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(245, 222, 179)); // #F5DEB3
        worksheet.Cells[1, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        worksheet.Row(1).Height = 40;

        // رأس الجدول
        var headers = new[] { "كود العميل", "اسم العميل", "الشخص المسؤول", "رقم الهاتف", "البريد الإلكتروني", "العنوان", "حد الائتمان", "الرصيد الحالي" };
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[3, i + 1].Value = headers[i];
        }
        
        // تنسيق رأس الجدول
        var headerRange = worksheet.Cells[3, 1, 3, 8];
        headerRange.Style.Font.Size = 12;
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Font.Color.SetColor(Color.White);
        headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
        headerRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(210, 105, 30)); // #D2691E
        headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        worksheet.Row(3).Height = 35;

        // بيانات العملاء
        int currentRow = 4;
        for (int i = 0; i < customers.Count; i++)
        {
            var customer = customers[i];
            bool isEvenRow = i % 2 == 0;
            
            // البيانات
            worksheet.Cells[currentRow, 1].Value = customer.Code;
            worksheet.Cells[currentRow, 2].Value = customer.Name;
            worksheet.Cells[currentRow, 3].Value = customer.ContactPerson ?? "";
            worksheet.Cells[currentRow, 4].Value = customer.Phone ?? "";
            worksheet.Cells[currentRow, 5].Value = customer.Email ?? "";
            worksheet.Cells[currentRow, 6].Value = customer.Address ?? "";
            worksheet.Cells[currentRow, 7].Value = (double)customer.CreditLimit;
            worksheet.Cells[currentRow, 8].Value = (double)customer.OpeningBalance;
            
            // تنسيق العمود الأول (كود العميل) - مميز
            worksheet.Cells[currentRow, 1].Style.Font.Bold = true;
            worksheet.Cells[currentRow, 1].Style.Font.Color.SetColor(Color.White);
            worksheet.Cells[currentRow, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[currentRow, 1].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(205, 133, 63)); // #CD853F
            worksheet.Cells[currentRow, 1].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            
            // تنسيق باقي الأعمدة (تدرج بين الأبيض والبرتقالي الفاتح)
            Color backgroundColor = isEvenRow ? 
                Color.FromArgb(255, 254, 247) : // #FFFEF7 (أبيض كريمي)
                Color.FromArgb(255, 248, 220);   // #FFF8DC (بيج فاتح)
            
            for (int col = 2; col <= 8; col++)
            {
                worksheet.Cells[currentRow, col].Style.Fill.PatternType = ExcelFillStyle.Solid;
                worksheet.Cells[currentRow, col].Style.Fill.BackgroundColor.SetColor(backgroundColor);
                worksheet.Cells[currentRow, col].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
            
            // تنسيق خاص للأرقام (الأعمدة 7 و 8)
            worksheet.Cells[currentRow, 7].Style.Font.Bold = true;
            worksheet.Cells[currentRow, 8].Style.Font.Bold = true;
            worksheet.Cells[currentRow, 7].Style.Numberformat.Format = "#,##0";
            worksheet.Cells[currentRow, 8].Style.Numberformat.Format = "#,##0";
            
            worksheet.Row(currentRow).Height = 30;
            currentRow++;
        }
    }
}

public class Customer
{
    public string Code { get; set; } = "";
    public string Name { get; set; } = "";
    public string? ContactPerson { get; set; }
    public string? Phone { get; set; }
    public string? Email { get; set; }
    public string? Address { get; set; }
    public decimal CreditLimit { get; set; }
    public decimal OpeningBalance { get; set; }
}
