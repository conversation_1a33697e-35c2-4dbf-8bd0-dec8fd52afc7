using System;
using System.IO;
using OfficeOpenXml;

class Program
{
    static void Main()
    {
        try
        {
            Console.WriteLine("🔧 بدء اختبار EPPlus...");
            
            // تعيين الترخيص
            ExcelPackage.LicenseContext = OfficeOpenXml.LicenseContext.NonCommercial;
            Console.WriteLine("✅ تم تعيين الترخيص");

            using (var package = new ExcelPackage())
            {
                Console.WriteLine("📝 إنشاء ورقة العمل...");
                var worksheet = package.Workbook.Worksheets.Add("اختبار");

                Console.WriteLine("📊 إضافة البيانات...");
                worksheet.Cells[1, 1].Value = "كود العميل";
                worksheet.Cells[1, 2].Value = "اسم العميل";
                worksheet.Cells[2, 1].Value = "C001";
                worksheet.Cells[2, 2].Value = "شركة الاختبار";

                Console.WriteLine("💾 حفظ الملف...");
                var fileInfo = new FileInfo("اختبار_EPPlus_بسيط.xlsx");
                package.SaveAs(fileInfo);
                
                Console.WriteLine($"✅ تم إنشاء الملف: {fileInfo.FullName}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ خطأ: {ex.Message}");
        }
    }
}
