<Window x:Class="InjazAcc.UI.Views.Shared.ColorPickerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="اختيار اللون" Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- عنوان النافذة -->
        <TextBlock Grid.Row="0" Text="اختر اللون المطلوب" FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- شبكة الألوان -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <WrapPanel x:Name="colorPanelContainer" Orientation="Horizontal" HorizontalAlignment="Center">
                <!-- سيتم إضافة الألوان برمجياً -->
            </WrapPanel>
        </ScrollViewer>

        <!-- معاينة اللون المختار -->
        <Border Grid.Row="2" Height="50" Margin="0,20,0,10" CornerRadius="5" BorderBrush="Gray" BorderThickness="1">
            <Border x:Name="colorPreviewContainer" Background="White" CornerRadius="5">
                <TextBlock Text="اللون المختار" HorizontalAlignment="Center" VerticalAlignment="Center" 
                          FontWeight="Bold" x:Name="colorPreviewText"/>
            </Border>
        </Border>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
            <Button x:Name="btnOK" Content="موافق" Width="80" Height="35" Margin="5" 
                   Style="{StaticResource MaterialDesignRaisedButton}" Click="btnOK_Click"/>
            <Button x:Name="btnCancel" Content="إلغاء" Width="80" Height="35" Margin="5" 
                   Style="{StaticResource MaterialDesignOutlinedButton}" Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>