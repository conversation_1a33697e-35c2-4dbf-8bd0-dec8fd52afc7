﻿#pragma checksum "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C0690B8893A6D8FE3B8004DA8F58F6C9D24AC436"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Settings {
    
    
    /// <summary>
    /// TableAndChartColorsPage
    /// </summary>
    public partial class TableAndChartColorsPage : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 49 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTableHeaderColor;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTableHeaderColor;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTableRowEvenColor;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTableRowEvenColor;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTableRowOddColor;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTableRowOddColor;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTableBorderColor;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTableBorderColor;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTableSelectedRowColor;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTableSelectedRowColor;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTableHoverRowColor;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTableHoverRowColor;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid previewDataGrid;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart1Color;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart1Color;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart2Color;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart2Color;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart3Color;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart3Color;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart4Color;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart4Color;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart5Color;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart5Color;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart6Color;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart6Color;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart7Color;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart7Color;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnChart8Color;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtChart8Color;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas chartPreview;
        
        #line default
        #line hidden
        
        
        #line 259 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSalesColor;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSalesColor;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPurchasesColor;
        
        #line default
        #line hidden
        
        
        #line 271 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPurchasesColor;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnInventoryColor;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtInventoryColor;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCustomersColor;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCustomersColor;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSuppliersColor;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSuppliersColor;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAccountingColor;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAccountingColor;
        
        #line default
        #line hidden
        
        
        #line 319 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnReportsColor;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtReportsColor;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSettingsColor;
        
        #line default
        #line hidden
        
        
        #line 331 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSettingsColor;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/settings/tableandchartcolorspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnTableHeaderColor = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnTableHeaderColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.txtTableHeaderColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 53 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtTableHeaderColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnTableRowEvenColor = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnTableRowEvenColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.txtTableRowEvenColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 63 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtTableRowEvenColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnTableRowOddColor = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnTableRowOddColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.txtTableRowOddColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 73 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtTableRowOddColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnTableBorderColor = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnTableBorderColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.txtTableBorderColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 83 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtTableBorderColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btnTableSelectedRowColor = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnTableSelectedRowColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.txtTableSelectedRowColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 93 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtTableSelectedRowColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnTableHoverRowColor = ((System.Windows.Controls.Button)(target));
            
            #line 99 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnTableHoverRowColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.txtTableHoverRowColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 103 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtTableHoverRowColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.previewDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 14:
            this.btnChart1Color = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart1Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.txtChart1Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 152 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart1Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btnChart2Color = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart2Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.txtChart2Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 162 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart2Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.btnChart3Color = ((System.Windows.Controls.Button)(target));
            
            #line 168 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart3Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.txtChart3Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 172 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart3Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 20:
            this.btnChart4Color = ((System.Windows.Controls.Button)(target));
            
            #line 178 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart4Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.txtChart4Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 182 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart4Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 22:
            this.btnChart5Color = ((System.Windows.Controls.Button)(target));
            
            #line 188 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart5Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.txtChart5Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 192 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart5Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            this.btnChart6Color = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart6Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.txtChart6Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 202 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart6Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 26:
            this.btnChart7Color = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart7Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.txtChart7Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 212 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart7Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 28:
            this.btnChart8Color = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnChart8Color.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.txtChart8Color = ((System.Windows.Controls.TextBox)(target));
            
            #line 222 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtChart8Color.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 30:
            this.chartPreview = ((System.Windows.Controls.Canvas)(target));
            return;
            case 31:
            this.btnSalesColor = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnSalesColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.txtSalesColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 263 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtSalesColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 33:
            this.btnPurchasesColor = ((System.Windows.Controls.Button)(target));
            
            #line 269 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnPurchasesColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.txtPurchasesColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 273 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtPurchasesColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 35:
            this.btnInventoryColor = ((System.Windows.Controls.Button)(target));
            
            #line 279 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnInventoryColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.txtInventoryColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 283 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtInventoryColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 37:
            this.btnCustomersColor = ((System.Windows.Controls.Button)(target));
            
            #line 289 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnCustomersColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.txtCustomersColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 293 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtCustomersColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 39:
            this.btnSuppliersColor = ((System.Windows.Controls.Button)(target));
            
            #line 299 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnSuppliersColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.txtSuppliersColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 303 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtSuppliersColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 41:
            this.btnAccountingColor = ((System.Windows.Controls.Button)(target));
            
            #line 309 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnAccountingColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.txtAccountingColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 313 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtAccountingColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 43:
            this.btnReportsColor = ((System.Windows.Controls.Button)(target));
            
            #line 319 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnReportsColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.txtReportsColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 323 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtReportsColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 45:
            this.btnSettingsColor = ((System.Windows.Controls.Button)(target));
            
            #line 329 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.btnSettingsColor.Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            this.txtSettingsColor = ((System.Windows.Controls.TextBox)(target));
            
            #line 333 "..\..\..\..\..\Views\Settings\TableAndChartColorsPage.xaml"
            this.txtSettingsColor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ColorTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

