﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Navigation;
using MaterialDesignThemes.Wpf;
using InjazAcc.Core.Exceptions;
using InjazAcc.Services.Helpers;
using InjazAcc.UI.Views;
using InjazAcc.UI.Views.Shared;

namespace InjazAcc.UI
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        // حالة النافذة (مكبرة أم لا)
        private bool _isMaximized = false;

        // الموقع والحجم الأصلي للنافذة
        private double _restoreLeft;
        private double _restoreTop;
        private double _restoreWidth;
        private double _restoreHeight;

        public MainWindow()
        {
            InitializeComponent();

            try
            {
                // حفظ الموقع والحجم الأصلي للنافذة
                _restoreLeft = Left;
                _restoreTop = Top;
                _restoreWidth = Width;
                _restoreHeight = Height;

                // تحميل لوحة التحكم افتراضياً
                this.Loaded += MainWindow_Loaded;

                // تحميل صفحة لوحة التحكم افتراضياً
                mainFrame.Navigate(new DashboardPage());

                // تحديد الصفحة الافتراضية (لوحة التحكم)
                menuDashboard.IsSelected = true;

                // تعيين اسم المستخدم ودوره
                txtUserName.Text = "مستخدم النظام";
                txtUserRole.Text = "مدير النظام";

                // إضافة تأثير حركي عند تحميل النافذة
                AnimateWindowOnLoad();
            }
            catch (Exception ex)
            {
                // استخدام نظام معالجة الاستثناءات الجديد
                ex.ToInjazException("حدث خطأ أثناء تهيئة النافذة الرئيسية").Handle(true);
            }
        }

        /// <summary>
        /// إضافة تأثير حركي عند تحميل النافذة
        /// </summary>
        private void AnimateWindowOnLoad()
        {
            // تأثير ظهور تدريجي للنافذة
            Opacity = 0;
            var animation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromSeconds(0.3)
            };
            BeginAnimation(UIElement.OpacityProperty, animation);
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحميل لوحة التحكم افتراضياً
                NavigateToPage("لوحة التحكم", PackIconKind.ViewDashboard, new DashboardPage());
                menuDashboard.IsSelected = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل لوحة التحكم: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void menuItem_Selected(object sender, RoutedEventArgs e)
        {
            try
            {
                // تحديد الصفحة المحددة
                ListViewItem selectedItem = sender as ListViewItem;
                selectedItem.ThrowIfNull("selectedItem", "لم يتم تحديد عنصر من القائمة");

                // تحديث عنوان الصفحة وأيقونتها وتحميل الصفحة المناسبة
                if (selectedItem == menuDashboard)
                {
                    NavigateToPage("لوحة التحكم", PackIconKind.ViewDashboard, new DashboardPage());
                }
                else if (selectedItem == menuSales)
                {
                    NavigateToPage("المبيعات", PackIconKind.CartOutline, new Views.Sales.SimpleSalesPage());
                }
                else if (selectedItem == menuPurchases)
                {
                    NavigateToPage("المشتريات", PackIconKind.ShoppingOutline, new Views.Purchases.SimplePurchasesPage());
                }
                else if (selectedItem == menuInventory)
                {
                    NavigateToPage("المخزون", PackIconKind.PackageVariantClosed, new Views.Inventory.InventoryPage());
                }
                else if (selectedItem == menuCustomers)
                {
                    NavigateToPage("العملاء", PackIconKind.AccountMultipleOutline, new Views.Customers.CustomersPage());
                }
                else if (selectedItem == menuSuppliers)
                {
                    NavigateToPage("الموردين", PackIconKind.TruckOutline, new Views.Suppliers.SuppliersPage());
                }
                else if (selectedItem == menuAccounting)
                {
                    NavigateToPage("الحسابات", PackIconKind.CashRegister, new Views.Accounts.AccountsPage());
                }
                else if (selectedItem == menuReports)
                {
                    NavigateToPage("التقارير", PackIconKind.ChartLine, new Views.Reports.ReportsPage());
                }
                else if (selectedItem == menuSettings)
                {
                    NavigateToPage("الإعدادات", PackIconKind.Cog, new Views.Settings.SettingsPage());
                }
            }
            catch (Exception ex)
            {
                // استخدام نظام معالجة الاستثناءات الجديد
                ex.ToInjazException("حدث خطأ أثناء تحميل الصفحة").Handle(true);
            }
        }

        private void menuItem_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // تحديد الصفحة المحددة
                ListViewItem selectedItem = sender as ListViewItem;
                if (selectedItem == null) return;

                // تحديث عنوان الصفحة وأيقونتها وتحميل الصفحة المناسبة
                if (selectedItem == menuDashboard)
                {
                    NavigateToPage("لوحة التحكم", PackIconKind.ViewDashboard, new DashboardPage());
                }
                else if (selectedItem == menuSales)
                {
                    NavigateToPage("المبيعات", PackIconKind.CartOutline, new Views.Sales.SimpleSalesPage());
                }
                else if (selectedItem == menuPurchases)
                {
                    NavigateToPage("المشتريات", PackIconKind.ShoppingOutline, new Views.Purchases.SimplePurchasesPage());
                }
                else if (selectedItem == menuInventory)
                {
                    NavigateToPage("المخزون", PackIconKind.PackageVariantClosed, new Views.Inventory.InventoryPage());
                }
                else if (selectedItem == menuCustomers)
                {
                    NavigateToPage("العملاء", PackIconKind.AccountMultipleOutline, new Views.Customers.CustomersPage());
                }
                else if (selectedItem == menuSuppliers)
                {
                    NavigateToPage("الموردين", PackIconKind.TruckOutline, new Views.Suppliers.SuppliersPage());
                }
                else if (selectedItem == menuAccounting)
                {
                    NavigateToPage("الحسابات", PackIconKind.CashRegister, new Views.Accounts.AccountsPage());
                }
                else if (selectedItem == menuReports)
                {
                    NavigateToPage("التقارير", PackIconKind.ChartLine, new Views.Reports.ReportsPage());
                }
                else if (selectedItem == menuSettings)
                {
                    NavigateToPage("الإعدادات", PackIconKind.Cog, new Views.Settings.SettingsPage());
                }

                // تحديد العنصر المختار
                selectedItem.IsSelected = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الصفحة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الانتقال إلى صفحة محددة مع تأثير حركي
        /// </summary>
        /// <param name="title">عنوان الصفحة</param>
        /// <param name="iconKind">نوع الأيقونة</param>
        /// <param name="page">الصفحة المراد الانتقال إليها</param>
        private void NavigateToPage(string title, PackIconKind iconKind, Page page)
        {
            try
            {
                // تحديث عنوان الصفحة وأيقونتها
                currentPageTitle.Text = title;
                currentPageIcon.Kind = iconKind;

                // إضافة تأثير حركي عند تغيير الصفحة
                var animation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = TimeSpan.FromSeconds(0.2)
                };

                // تحميل الصفحة الجديدة
                mainFrame.Navigate(page);
                mainFrame.Opacity = 0;
                mainFrame.BeginAnimation(UIElement.OpacityProperty, animation);
            }
            catch (Exception ex)
            {
                throw new BusinessException($"حدث خطأ أثناء تحميل صفحة {title}", ErrorCodes.BusinessRuleViolation, ex);
            }
        }

        private void btnLogout_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تأكيد تسجيل الخروج باستخدام نظام معالجة الاستثناءات
                var confirmationMessage = new InjazAccException("هل أنت متأكد من رغبتك في تسجيل الخروج؟", "CONFIRM-001", ErrorSeverity.Question);

                // عرض رسالة تأكيد
                MessageBoxResult result = MessageBox.Show(
                    confirmationMessage.Message,
                    "تسجيل الخروج",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إضافة تأثير حركي عند تسجيل الخروج
                    var animation = new DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        Duration = TimeSpan.FromSeconds(0.3)
                    };

                    animation.Completed += (s, args) =>
                    {
                        // فتح نافذة تسجيل الدخول
                        LoginWindow loginWindow = new LoginWindow();
                        loginWindow.Show();

                        // إغلاق النافذة الرئيسية
                        this.Close();
                    };

                    BeginAnimation(UIElement.OpacityProperty, animation);
                }
            }
            catch (Exception ex)
            {
                // استخدام نظام معالجة الاستثناءات الجديد
                ex.ToInjazException("حدث خطأ أثناء تسجيل الخروج").Handle(true);
            }
        }

        /// <summary>
        /// تصغير النافذة
        /// </summary>
        private void btnMinimize_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        /// <summary>
        /// تكبير/استعادة النافذة
        /// </summary>
        private void btnMaximize_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_isMaximized)
                {
                    // استعادة النافذة إلى الحجم الأصلي
                    Left = _restoreLeft;
                    Top = _restoreTop;
                    Width = _restoreWidth;
                    Height = _restoreHeight;
                    _isMaximized = false;

                    // تغيير أيقونة الزر
                    ((PackIcon)btnMaximize.Content).Kind = PackIconKind.WindowMaximize;
                }
                else
                {
                    // حفظ الموقع والحجم الحالي
                    _restoreLeft = Left;
                    _restoreTop = Top;
                    _restoreWidth = Width;
                    _restoreHeight = Height;

                    // تكبير النافذة لتملأ الشاشة
                    Left = 0;
                    Top = 0;
                    Width = SystemParameters.WorkArea.Width;
                    Height = SystemParameters.WorkArea.Height;
                    _isMaximized = true;

                    // تغيير أيقونة الزر
                    ((PackIcon)btnMaximize.Content).Kind = PackIconKind.WindowRestore;
                }
            }
            catch (Exception ex)
            {
                // استخدام نظام معالجة الاستثناءات الجديد
                ex.ToInjazException("حدث خطأ أثناء تغيير حجم النافذة").Handle(true);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void btnCloseMain_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تأكيد الخروج من التطبيق
                var confirmationMessage = new InjazAccException("هل أنت متأكد من رغبتك في إغلاق التطبيق؟", "CONFIRM-002", ErrorSeverity.Question);

                // عرض رسالة تأكيد
                MessageBoxResult result = MessageBox.Show(
                    confirmationMessage.Message,
                    "إغلاق التطبيق",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إضافة تأثير حركي عند الإغلاق
                    var animation = new DoubleAnimation
                    {
                        From = 1,
                        To = 0,
                        Duration = TimeSpan.FromSeconds(0.3)
                    };

                    animation.Completed += (s, args) =>
                    {
                        // إغلاق التطبيق
                        Application.Current.Shutdown();
                    };

                    BeginAnimation(UIElement.OpacityProperty, animation);
                }
            }
            catch (Exception ex)
            {
                // استخدام نظام معالجة الاستثناءات الجديد
                ex.ToInjazException("حدث خطأ أثناء إغلاق التطبيق").Handle(true);
            }
        }

        /// <summary>
        /// تحريك النافذة عند الضغط على الماوس
        /// </summary>
        private void Window_MouseDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.ChangedButton == MouseButton.Left && e.ButtonState == MouseButtonState.Pressed)
                {
                    DragMove();
                }
            }
            catch (Exception ex)
            {
                // تجاهل الخطأ لتجنب توقف التطبيق
                Console.WriteLine($"خطأ في تحريك النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح نافذة البحث
        /// </summary>
        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء وعرض نافذة البحث
                MessageBox.Show("سيتم تطوير نافذة البحث قريباً", "البحث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح نافذة البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض الإشعارات
        /// </summary>
        private void btnNotifications_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("لديك 3 إشعارات جديدة:\n\n• تم إضافة فاتورة مبيعات جديدة\n• تنبيه: مخزون منتج منخفض\n• تذكير: استحقاق دفعة للمورد",
                    "الإشعارات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض الإشعارات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض المساعدة
        /// </summary>
        private void btnHelp_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string helpMessage = "مرحباً بك في برنامج إنجاز المحاسبي\n\n" +
                    "للمساعدة والدعم:\n" +
                    "• البريد الإلكتروني: <EMAIL>\n" +
                    "• الهاتف: 966-11-1234567\n" +
                    "• الموقع الإلكتروني: www.injazacc.com\n\n" +
                    "يمكنك أيضاً الضغط على F1 في أي وقت للحصول على المساعدة";

                MessageBox.Show(helpMessage, "المساعدة والدعم", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء عرض المساعدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// فتح الإعدادات السريعة
        /// </summary>
        private void btnSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                NavigateToPage("الإعدادات", PackIconKind.Cog, new Views.Settings.SettingsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح الإعدادات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طريقة عامة للانتقال إلى صفحة (تستخدم من قبل نوافذ أخرى)
        /// </summary>
        /// <param name="page">الصفحة المراد الانتقال إليها</param>
        public void NavigateToPage(Page page)
        {
            try
            {
                // تحديد القائمة المناسبة بناءً على نوع الصفحة
                if (page is Views.DashboardPage)
                {
                    menuDashboard.IsSelected = true;
                }
                else if (page is Views.Sales.SimpleSalesPage || page is Views.Sales.SalesPage)
                {
                    menuSales.IsSelected = true;
                }
                else if (page is Views.Purchases.SimplePurchasesPage)
                {
                    menuPurchases.IsSelected = true;
                }
                else if (page is Views.Inventory.InventoryPage)
                {
                    menuInventory.IsSelected = true;
                }
                else if (page is Views.Customers.CustomersPage)
                {
                    menuCustomers.IsSelected = true;
                }
                else if (page is Views.Suppliers.SuppliersPage)
                {
                    menuSuppliers.IsSelected = true;
                }
                else if (page is Views.Accounts.AccountsPage ||
                         page is Views.Accounts.ChartOfAccountsPage ||
                         page is Views.Accounts.CashPage ||
                         page is Views.Accounts.FinancialStatementsPage ||
                         page is Views.Accounts.LedgerPage ||
                         page is Views.Accounts.OpeningBalancesPage)
                {
                    menuAccounting.IsSelected = true;
                }
                else if (page is Views.Reports.ReportsPage ||
                         page is Views.Reports.SalesReportsPage ||
                         page is Views.Reports.PurchasesReportsPage ||
                         page is Views.Reports.InventoryReportsPage ||
                         page is Views.Reports.CustomersAndSuppliersReportsPage)
                {
                    menuReports.IsSelected = true;
                }
                else if (page is Views.Settings.SettingsPage)
                {
                    menuSettings.IsSelected = true;
                }

                // تحميل الصفحة مباشرة
                mainFrame.Navigate(page);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الانتقال إلى الصفحة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}