# 🎨 برنامج إنجاز المحاسبي - نظام التحكم الشامل بالألوان

## 🌟 نظام متطور للتحكم الكامل في كل لون في البرنامج

---

## 📋 نظرة عامة

برنامج إنجاز المحاسبي هو نظام محاسبي شامل مع **نظام تحكم متقدم بالألوان** يوفر:

### ✨ المميزات الرئيسية:
- **🎨 التحكم في +80 لون مختلف** في البرنامج
- **📱 واجهة سهلة الاستخدام** مع 6 تبويبات منظمة
- **👁️ معاينة فورية** لجميع التغييرات
- **🎭 8 قوالب ألوان جاهزة** للاستخدام السريع
- **🔧 منتقي ألوان متقدم** مع عجلة الألوان
- **💾 تصدير واستيراد** إعدادات الألوان
- **🔄 تطبيق تلقائي** على جميع عناصر الواجهة
- **♿ دعم إمكانية الوصول** للجميع

---

## 🚀 التشغيل السريع

### المتطلبات:
- **Windows 10/11**
- **.NET 6.0 أو أحدث**
- **4 GB RAM** على الأقل
- **500 MB** مساحة فارغة

### طريقة التشغيل:

#### الطريقة الأولى: باستخدام Python
```bash
python run_injazacc.py
```

#### الطريقة الثانية: باستخدام .NET مباشرة
```bash
cd injaz_acc/src/InjazAcc.UI
dotnet run
```

#### الطريقة الثالثة: بناء وتشغيل
```bash
cd injaz_acc/src/InjazAcc.UI
dotnet build
dotnet run
```

---

## 🎨 نظام التحكم بالألوان

### 📊 إحصائيات النظام:
- **83 لون** قابل للتخصيص
- **6 تبويبات** منظمة
- **8 قوالب** جاهزة
- **معاينة فورية** للتغييرات

### 🎯 الألوان المدعومة:

#### 🌈 الألوان الأساسية (7 ألوان):
- اللون الأساسي والداكن والفاتح
- اللون الثانوي والداكن والفاتح
- لون التمييز

#### 🏠 ألوان الخلفية (7 ألوان):
- خلفية التطبيق والسطح والبطاقات
- خلفية الحوارات والشريط الجانبي
- خلفية الرأس والتذييل

#### 📝 ألوان النصوص (8 ألوان):
- النص الأساسي والثانوي والمعطل
- نص التلميح والنص على الألوان المختلفة

#### 🔲 ألوان الحدود (5 ألوان):
- حدود عامة وفاصلة ومخطط
- حدود التركيز والخطأ

#### 🔘 ألوان الأزرار (10 ألوان):
- أزرار أساسية وثانوية
- حالات التمرير والضغط والتعطيل

#### 🚦 ألوان الحالة (17 لون):
- ألوان النجاح والتحذير والخطأ والمعلومات
- ألوان التحديد والتمرير والتركيز

#### 📊 ألوان الجداول (6 ألوان):
- رأس الجدول والصفوف الزوجية والفردية
- الصف المحدد وعند التمرير

#### 📈 ألوان الرسوم البيانية (8 ألوان):
- 8 ألوان متميزة للرسوم البيانية

#### 🏢 ألوان الأقسام (8 ألوان):
- لون مميز لكل قسم في البرنامج

---

## 🎭 القوالب الجاهزة

### 1. 🎨 القالب الافتراضي
- ألوان متوازنة ومريحة للعين
- مناسب للاستخدام اليومي

### 2. 🌙 القالب الداكن
- خلفية داكنة ونصوص فاتحة
- يقلل إجهاد العين

### 3. 💙 القالب الأزرق
- يعتمد على درجات الأزرق
- يعطي شعور بالثقة والاستقرار

### 4. 💚 القالب الأخضر
- يعتمد على درجات الأخضر
- يعطي شعور بالنمو والازدهار

### 5. 💜 القالب البنفسجي
- يعتمد على درجات البنفسجي
- يعطي شعور بالإبداع والتميز

### 6. 🧡 القالب البرتقالي
- يعتمد على درجات البرتقالي
- يعطي شعور بالطاقة والحيوية

### 7. ❤️ القالب الأحمر
- يعتمد على درجات الأحمر
- يعطي شعور بالقوة والجرأة

### 8. ⚫ قالب التباين العالي
- ألوان عالية التباين
- مناسب لذوي الاحتياجات الخاصة

---

## 🛠️ البنية التقنية

### 🏗️ المكونات الرئيسية:

#### 1. **AdvancedColorManager**
- إدارة شاملة لجميع الألوان
- حفظ وتحميل الإعدادات
- تطبيق القوالب الجاهزة

#### 2. **UIColorApplier**
- تطبيق الألوان على عناصر الواجهة
- تحديث تلقائي للعناصر الجديدة
- دعم جميع أنواع العناصر

#### 3. **ChartColorManager**
- إدارة ألوان الرسوم البيانية
- إنشاء تدرجات لونية
- دعم أنواع مختلفة من الرسوم

#### 4. **SectionColorManager**
- إدارة ألوان أقسام البرنامج
- تطبيق الألوان على القوائم
- إنشاء أيقونات ملونة

#### 5. **AdvancedColorPickerWindow**
- منتقي ألوان متقدم
- عجلة ألوان تفاعلية
- دعم قيم RGB وHSV وHex

### 📁 هيكل المشروع:
```
injaz_acc/
├── src/
│   ├── InjazAcc.Core/          # النواة الأساسية
│   ├── InjazAcc.DataAccess/    # طبقة الوصول للبيانات
│   ├── InjazAcc.Services/      # طبقة الخدمات
│   └── InjazAcc.UI/           # واجهة المستخدم
│       ├── Helpers/           # المساعدات
│       │   ├── AdvancedColorManager.cs
│       │   ├── UIColorApplier.cs
│       │   ├── ChartColorManager.cs
│       │   └── SectionColorManager.cs
│       ├── Views/
│       │   ├── Settings/      # صفحات الإعدادات
│       │   └── Shared/        # العناصر المشتركة
│       └── Resources/         # الموارد والألوان
├── docs/                      # الوثائق
├── run_injazacc.py           # ملف التشغيل
└── README.md                 # هذا الملف
```

---

## 📚 الوثائق التفصيلية

### 📖 أدلة المستخدم:
- **[دليل التحكم الشامل بالألوان](دليل_التحكم_الشامل_بالألوان.md)** - دليل شامل للمستخدمين
- **[README التقني](README_نظام_التحكم_الشامل_بالألوان.md)** - وثائق تقنية مفصلة

### 🔧 للمطورين:
- **API Documentation** - وثائق واجهة البرمجة
- **Architecture Guide** - دليل البنية التقنية
- **Contributing Guide** - دليل المساهمة

---

## 🎯 كيفية الاستخدام

### 1. 🚀 البدء السريع:
1. شغل البرنامج باستخدام `python run_injazacc.py`
2. انتقل إلى **الإعدادات** → **التحكم المتقدم بالألوان**
3. اختر قالب جاهز أو خصص الألوان يدوياً
4. شاهد المعاينة الفورية واحفظ الإعدادات

### 2. 🎨 تخصيص الألوان:
1. **اختر التبويب المناسب** (أساسية، خلفية، نصوص، إلخ)
2. **انقر على زر اللون** المراد تغييره
3. **اختر اللون الجديد** من منتقي الألوان
4. **شاهد التغيير فوراً** في المعاينة
5. **احفظ الإعدادات** للاحتفاظ بالتغييرات

### 3. 📤 تصدير واستيراد:
- **تصدير**: احفظ إعداداتك في ملف JSON
- **استيراد**: حمّل إعدادات من ملف خارجي
- **مشاركة**: شارك الألوان مع فريق العمل

---

## 🧪 اختبار النظام

### 🔍 صفحة اختبار الألوان:
- **معاينة شاملة** لجميع الألوان
- **جداول تجريبية** بالبيانات الحقيقية
- **رسوم بيانية تفاعلية** بالألوان الجديدة
- **عناصر واجهة متنوعة** للاختبار

### ✅ اختبارات الجودة:
- **اختبار التباين** للنصوص والخلفيات
- **اختبار عمى الألوان** مع محاكيات
- **اختبار الأداء** مع عدد كبير من العناصر
- **اختبار إمكانية الوصول** للجميع

---

## 🔧 استكشاف الأخطاء

### ❓ المشاكل الشائعة:

#### 🚫 البرنامج لا يعمل:
- تأكد من تثبيت .NET 6.0 أو أحدث
- تحقق من وجود جميع ملفات المشروع
- جرب إعادة بناء المشروع

#### 🎨 الألوان لا تظهر:
- تأكد من حفظ الإعدادات
- أعد تشغيل البرنامج
- تحقق من صحة أكواد الألوان

#### 🐌 البرنامج بطيء:
- قلل من استخدام الألوان المعقدة
- أغلق التطبيقات الأخرى
- تحقق من مواصفات الجهاز

### 🆘 الحصول على المساعدة:
1. راجع الوثائق أولاً
2. تحقق من الأخطاء في وحدة التحكم
3. جرب إعادة تعيين الألوان للافتراضية
4. تواصل مع فريق الدعم

---

## 🔮 التطوير المستقبلي

### 🎯 المميزات المخططة:
- **🎨 محرر ألوان متقدم** مع أدوات احترافية
- **🌐 مزامنة الألوان** عبر الشبكة
- **🤖 مولد قوالب ذكي** بالذكاء الاصطناعي
- **📱 تطبيق جوال** للتحكم عن بُعد
- **🎵 ألوان ديناميكية** تتغير حسب الوقت
- **📊 تحليل إمكانية الوصول** التلقائي

### 🔧 التحسينات التقنية:
- **⚡ تحسين الأداء** مع عدد كبير من العناصر
- **🌈 دعم الألوان المتحركة** والتدرجات
- **🔌 API للمطورين** لإضافة ألوان مخصصة
- **🎨 دعم أنظمة ألوان متقدمة** (HSL, LAB, CMYK)

---

## 👥 المساهمة

### 🤝 كيفية المساهمة:
1. **Fork** المشروع
2. **إنشاء فرع** للميزة الجديدة
3. **إضافة التحسينات** مع الاختبارات
4. **إرسال Pull Request** مع وصف مفصل

### 📋 إرشادات المساهمة:
- اتبع معايير الكود الموجودة
- أضف اختبارات للميزات الجديدة
- وثق التغييرات في README
- استخدم رسائل commit واضحة

---

## 📄 الترخيص

هذا المشروع مرخص تحت **رخصة MIT** - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

## 🙏 شكر وتقدير

### 💝 شكر خاص لـ:
- **Material Design in XAML** - للواجهة الجميلة
- **Microsoft .NET Team** - للمنصة الرائعة
- **مجتمع المطورين** - للدعم والمساهمات

---

## 📞 التواصل

### 📧 معلومات التواصل:
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الرسمي**: https://injazacc.com
- **الدعم التقني**: https://support.injazacc.com

### 🌐 وسائل التواصل:
- **GitHub**: https://github.com/injazacc
- **LinkedIn**: https://linkedin.com/company/injazacc
- **Twitter**: https://twitter.com/injazacc

---

## 📊 إحصائيات المشروع

### 📈 أرقام مثيرة للإعجاب:
- **83 لون** قابل للتخصيص
- **8 قوالب** جاهزة للاستخدام
- **6 تبويبات** منظمة
- **+3500 سطر** من الكود
- **معاينة فورية** للتغييرات
- **دعم كامل** لإمكانية الوصول

---

**🌈 برنامج إنجاز المحاسبي - حيث تلتقي المحاسبة بالإبداع!**

*تم تطوير هذا النظام ليكون الأكثر تقدماً وشمولية في مجال تخصيص ألوان التطبيقات المحاسبية.* ✨

---

*آخر تحديث: ديسمبر 2024*