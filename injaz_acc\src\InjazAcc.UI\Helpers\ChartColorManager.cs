using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Media;

namespace InjazAcc.UI.Helpers
{
    /// <summary>
    /// مدير ألوان الرسوم البيانية
    /// </summary>
    public static class ChartColorManager
    {
        /// <summary>
        /// الحصول على ألوان الرسوم البيانية
        /// </summary>
        public static List<Color> GetChartColors()
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                return new List<Color>
                {
                    (Color)ColorConverter.ConvertFromString(settings.Chart1Color),
                    (Color)ColorConverter.ConvertFromString(settings.Chart2Color),
                    (Color)ColorConverter.ConvertFromString(settings.Chart3Color),
                    (Color)ColorConverter.ConvertFromString(settings.Chart4Color),
                    (Color)ColorConverter.ConvertFromString(settings.Chart5Color),
                    (Color)ColorConverter.ConvertFromString(settings.Chart6Color),
                    (Color)ColorConverter.ConvertFromString(settings.Chart7Color),
                    (Color)ColorConverter.ConvertFromString(settings.Chart8Color)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على ألوان الرسوم البيانية: {ex.Message}");
                return GetDefaultChartColors();
            }
        }

        /// <summary>
        /// الحصول على ألوان الرسوم البيانية الافتراضية
        /// </summary>
        public static List<Color> GetDefaultChartColors()
        {
            return new List<Color>
            {
                Color.FromRgb(33, 150, 243),   // أزرق
                Color.FromRgb(76, 175, 80),    // أخضر
                Color.FromRgb(255, 152, 0),    // برتقالي
                Color.FromRgb(156, 39, 176),   // بنفسجي
                Color.FromRgb(244, 67, 54),    // أحمر
                Color.FromRgb(0, 188, 212),    // سماوي
                Color.FromRgb(139, 195, 74),   // أخضر فاتح
                Color.FromRgb(255, 193, 7)     // أصفر
            };
        }

        /// <summary>
        /// الحصول على فرش الرسوم البيانية
        /// </summary>
        public static List<SolidColorBrush> GetChartBrushes()
        {
            return GetChartColors().Select(color => new SolidColorBrush(color)).ToList();
        }

        /// <summary>
        /// الحصول على لون رسم بياني محدد
        /// </summary>
        public static Color GetChartColor(int index)
        {
            var colors = GetChartColors();
            return colors[index % colors.Count];
        }

        /// <summary>
        /// الحصول على فرشاة رسم بياني محددة
        /// </summary>
        public static SolidColorBrush GetChartBrush(int index)
        {
            return new SolidColorBrush(GetChartColor(index));
        }

        /// <summary>
        /// إنشاء تدرج لوني للرسم البياني
        /// </summary>
        public static LinearGradientBrush CreateChartGradient(int index, double opacity = 1.0)
        {
            try
            {
                var baseColor = GetChartColor(index);
                var lightColor = Color.FromArgb(
                    (byte)(opacity * 255 * 0.3),
                    baseColor.R,
                    baseColor.G,
                    baseColor.B);
                var darkColor = Color.FromArgb(
                    (byte)(opacity * 255),
                    baseColor.R,
                    baseColor.G,
                    baseColor.B);

                var gradient = new LinearGradientBrush();
                gradient.StartPoint = new System.Windows.Point(0, 0);
                gradient.EndPoint = new System.Windows.Point(0, 1);
                gradient.GradientStops.Add(new GradientStop(lightColor, 0));
                gradient.GradientStops.Add(new GradientStop(darkColor, 1));

                return gradient;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء تدرج الرسم البياني: {ex.Message}");
                return new LinearGradientBrush(Colors.LightBlue, Colors.Blue, 90);
            }
        }

        /// <summary>
        /// الحصول على ألوان متباينة للرسوم البيانية
        /// </summary>
        public static List<Color> GetContrastingColors(int count)
        {
            try
            {
                var colors = GetChartColors();
                var result = new List<Color>();

                // إذا كان العدد المطلوب أقل من أو يساوي الألوان المتاحة
                if (count <= colors.Count)
                {
                    return colors.Take(count).ToList();
                }

                // إذا كان العدد أكبر، نولد ألوان إضافية
                result.AddRange(colors);
                
                for (int i = colors.Count; i < count; i++)
                {
                    // توليد لون جديد بناءً على HSV
                    double hue = (360.0 / count) * i;
                    double saturation = 0.7 + (0.3 * (i % 3) / 3.0);
                    double value = 0.8 + (0.2 * (i % 2));
                    
                    var color = HsvToRgb(hue, saturation, value);
                    result.Add(color);
                }

                return result.Take(count).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على ألوان متباينة: {ex.Message}");
                return GetDefaultChartColors().Take(count).ToList();
            }
        }

        /// <summary>
        /// تحويل HSV إلى RGB
        /// </summary>
        private static Color HsvToRgb(double hue, double saturation, double value)
        {
            int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            double f = hue / 60 - Math.Floor(hue / 60);

            value = value * 255;
            int v = Convert.ToInt32(value);
            int p = Convert.ToInt32(value * (1 - saturation));
            int q = Convert.ToInt32(value * (1 - f * saturation));
            int t = Convert.ToInt32(value * (1 - (1 - f) * saturation));

            return hi switch
            {
                0 => Color.FromRgb((byte)v, (byte)t, (byte)p),
                1 => Color.FromRgb((byte)q, (byte)v, (byte)p),
                2 => Color.FromRgb((byte)p, (byte)v, (byte)t),
                3 => Color.FromRgb((byte)p, (byte)q, (byte)v),
                4 => Color.FromRgb((byte)t, (byte)p, (byte)v),
                _ => Color.FromRgb((byte)v, (byte)p, (byte)q),
            };
        }

        /// <summary>
        /// الحصول على لون مكمل
        /// </summary>
        public static Color GetComplementaryColor(Color color)
        {
            return Color.FromRgb(
                (byte)(255 - color.R),
                (byte)(255 - color.G),
                (byte)(255 - color.B));
        }

        /// <summary>
        /// الحصول على لون أفتح
        /// </summary>
        public static Color GetLighterColor(Color color, double factor = 0.3)
        {
            return Color.FromRgb(
                (byte)Math.Min(255, color.R + (255 - color.R) * factor),
                (byte)Math.Min(255, color.G + (255 - color.G) * factor),
                (byte)Math.Min(255, color.B + (255 - color.B) * factor));
        }

        /// <summary>
        /// الحصول على لون أغمق
        /// </summary>
        public static Color GetDarkerColor(Color color, double factor = 0.3)
        {
            return Color.FromRgb(
                (byte)(color.R * (1 - factor)),
                (byte)(color.G * (1 - factor)),
                (byte)(color.B * (1 - factor)));
        }

        /// <summary>
        /// تطبيق ألوان الرسوم البيانية على عنصر
        /// </summary>
        public static void ApplyChartColorsToElement(System.Windows.FrameworkElement element)
        {
            try
            {
                // يمكن تخصيص هذه الطريقة حسب نوع الرسم البياني المستخدم
                // مثل LiveCharts أو OxyPlot أو غيرها
                
                var colors = GetChartColors();
                
                // تطبيق الألوان على الموارد
                if (element.Resources != null)
                {
                    for (int i = 0; i < colors.Count; i++)
                    {
                        var colorKey = $"ChartColor{i + 1}";
                        var brushKey = $"ChartBrush{i + 1}";
                        
                        element.Resources[colorKey] = colors[i];
                        element.Resources[brushKey] = new SolidColorBrush(colors[i]);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان الرسوم البيانية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء مجموعة ألوان للفطائر البيانية
        /// </summary>
        public static List<Color> CreatePieChartColors(int sliceCount)
        {
            try
            {
                if (sliceCount <= 8)
                {
                    return GetChartColors().Take(sliceCount).ToList();
                }

                var colors = new List<Color>();
                var baseColors = GetChartColors();
                
                // استخدام الألوان الأساسية أولاً
                colors.AddRange(baseColors);
                
                // إنشاء تدرجات للألوان الإضافية
                for (int i = baseColors.Count; i < sliceCount; i++)
                {
                    var baseColor = baseColors[i % baseColors.Count];
                    var variation = (i / baseColors.Count) * 0.2;
                    
                    if (i % 2 == 0)
                    {
                        colors.Add(GetLighterColor(baseColor, variation));
                    }
                    else
                    {
                        colors.Add(GetDarkerColor(baseColor, variation));
                    }
                }

                return colors.Take(sliceCount).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ألوان الفطائر البيانية: {ex.Message}");
                return GetDefaultChartColors().Take(sliceCount).ToList();
            }
        }

        /// <summary>
        /// إنشاء مجموعة ألوان للأعمدة البيانية
        /// </summary>
        public static List<Color> CreateBarChartColors(int barCount, bool useGradient = false)
        {
            try
            {
                var colors = GetContrastingColors(barCount);
                
                if (useGradient && colors.Count > 1)
                {
                    // إنشاء تدرج بين الألوان
                    var gradientColors = new List<Color>();
                    var firstColor = colors.First();
                    var lastColor = colors.Last();
                    
                    for (int i = 0; i < barCount; i++)
                    {
                        double ratio = (double)i / (barCount - 1);
                        var r = (byte)(firstColor.R + (lastColor.R - firstColor.R) * ratio);
                        var g = (byte)(firstColor.G + (lastColor.G - firstColor.G) * ratio);
                        var b = (byte)(firstColor.B + (lastColor.B - firstColor.B) * ratio);
                        
                        gradientColors.Add(Color.FromRgb(r, g, b));
                    }
                    
                    return gradientColors;
                }

                return colors;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء ألوان الأعمدة البيانية: {ex.Message}");
                return GetDefaultChartColors().Take(barCount).ToList();
            }
        }

        /// <summary>
        /// الحصول على لون حسب القيمة (خريطة حرارية)
        /// </summary>
        public static Color GetHeatMapColor(double value, double min, double max)
        {
            try
            {
                // تطبيع القيمة بين 0 و 1
                double normalizedValue = Math.Max(0, Math.Min(1, (value - min) / (max - min)));
                
                // إنشاء تدرج من الأزرق (بارد) إلى الأحمر (ساخن)
                if (normalizedValue < 0.5)
                {
                    // من الأزرق إلى الأخضر
                    double ratio = normalizedValue * 2;
                    return Color.FromRgb(
                        0,
                        (byte)(255 * ratio),
                        (byte)(255 * (1 - ratio)));
                }
                else
                {
                    // من الأخضر إلى الأحمر
                    double ratio = (normalizedValue - 0.5) * 2;
                    return Color.FromRgb(
                        (byte)(255 * ratio),
                        (byte)(255 * (1 - ratio)),
                        0);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على لون الخريطة الحرارية: {ex.Message}");
                return Colors.Gray;
            }
        }

        /// <summary>
        /// تحديث ألوان الرسوم البيانية في التطبيق
        /// </summary>
        public static void UpdateChartColorsInApplication()
        {
            try
            {
                var colors = GetChartColors();
                var app = System.Windows.Application.Current;
                
                if (app?.Resources != null)
                {
                    for (int i = 0; i < colors.Count; i++)
                    {
                        var colorKey = $"Chart{i + 1}Color";
                        var brushKey = $"Chart{i + 1}Brush";
                        
                        app.Resources[colorKey] = colors[i];
                        app.Resources[brushKey] = new SolidColorBrush(colors[i]);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث ألوان الرسوم البيانية: {ex.Message}");
            }
        }
    }
}