using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Dialogs
{
    public partial class AddInvoiceDialog : Window
    {
        public class InvoiceItem
        {
            public string ProductCode { get; set; }
            public string ProductName { get; set; }
            public decimal Quantity { get; set; }
            public decimal UnitPrice { get; set; }
            public decimal Discount { get; set; }
            public decimal Total => (Quantity * UnitPrice) - Discount;
        }

        public ObservableCollection<InvoiceItem> InvoiceItems { get; set; }
        public bool IsSuccess { get; private set; }

        public AddInvoiceDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            InvoiceItems = new ObservableCollection<InvoiceItem>();
            dgItems.ItemsSource = InvoiceItems;
            
            // تعيين التاريخ الحالي
            dpInvoiceDate.SelectedDate = DateTime.Now;
            dpDueDate.SelectedDate = DateTime.Now.AddDays(30);
            
            // تعيين القيم الافتراضية
            cmbInvoiceType.SelectedIndex = 0; // فاتورة بيع
            cmbPaymentMethod.SelectedIndex = 0; // نقداً
            
            // تركيز على رقم الفاتورة
            txtInvoiceNumber.Focus();
            
            // ربط الأحداث
            InvoiceItems.CollectionChanged += (s, e) => CalculateTotals();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtInvoiceNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtInvoiceNumber.Focus();
                return false;
            }

            if (dpInvoiceDate.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار تاريخ الفاتورة", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                dpInvoiceDate.Focus();
                return false;
            }

            if (cmbInvoiceType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الفاتورة", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                cmbInvoiceType.Focus();
                return false;
            }

            if (cmbCustomer.SelectedItem == null && string.IsNullOrWhiteSpace(cmbCustomer.Text))
            {
                MessageBox.Show("يرجى اختيار العميل أو المورد", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                cmbCustomer.Focus();
                return false;
            }

            if (InvoiceItems.Count == 0)
            {
                MessageBox.Show("يرجى إضافة صنف واحد على الأقل للفاتورة", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void CalculateTotals()
        {
            decimal subTotal = InvoiceItems.Sum(item => item.Total);
            decimal totalDiscount = decimal.TryParse(txtTotalDiscount.Text, out decimal discount) ? discount : 0;
            decimal tax = decimal.TryParse(txtTax.Text, out decimal taxAmount) ? taxAmount : 0;
            
            txtSubTotal.Text = subTotal.ToString("F2");
            txtGrandTotal.Text = (subTotal - totalDiscount + tax).ToString("F2");
        }

        private void btnAddItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إضافة صنف بسيط للاختبار
                var item = new InvoiceItem
                {
                    ProductCode = "P001",
                    ProductName = "منتج تجريبي",
                    Quantity = 1,
                    UnitPrice = 100,
                    Discount = 0
                };
                
                InvoiceItems.Add(item);
                
                MessageBox.Show("تم إضافة صنف تجريبي. يمكنك تعديل البيانات في الجدول.", "تم الإضافة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إضافة الصنف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnRemoveItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (dgItems.SelectedItem is InvoiceItem selectedItem)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذا الصنف؟", "تأكيد الحذف", 
                        MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        InvoiceItems.Remove(selectedItem);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار صنف للحذف", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حذف الصنف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                IsSuccess = true;
                
                MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح الحفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم طباعة الفاتورة", "طباعة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تنفيذ الطباعة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            IsSuccess = false;
            DialogResult = false;
            Close();
        }
    }
}
