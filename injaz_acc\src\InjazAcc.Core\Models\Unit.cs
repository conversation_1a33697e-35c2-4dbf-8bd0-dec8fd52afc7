using System.Collections.Generic;

namespace InjazAcc.Core.Models
{
    /// <summary>
    /// نموذج وحدة القياس للمنتجات
    /// </summary>
    public class Unit
    {
        public int Id { get; set; }
        public string? Name { get; set; }
        public string? Symbol { get; set; }
        public string? Description { get; set; }
        
        // العلاقة مع المنتجات
        public virtual ICollection<Product>? Products { get; set; }
    }
}
