@echo off
chcp 65001 > nul
echo ========================================
echo اختبار التصميم الجديد لتقارير Excel
echo ========================================
echo.

echo جاري بناء المشروع...
dotnet build injaz_acc\src\InjazAcc.UI\InjazAcc.UI.csproj

if %ERRORLEVEL% EQU 0 (
    echo ✅ تم بناء المشروع بنجاح
    echo.
    echo يمكنك الآن تشغيل البرنامج واختبار التقارير الجديدة:
    echo 1. اذهب إلى صفحة العملاء واضغط "تصدير إلى Excel"
    echo 2. اختر نوع الملف .xls للحصول على التصميم الجميل
    echo 3. كرر نفس الخطوات للموردين والمنتجات
    echo.
    echo التحسينات الجديدة:
    echo - تصميم جميل بألوان برتقالية متدرجة
    echo - العمود الأول مميز بلون داكن
    echo - صفوف متدرجة بين الأبيض والبيج الفاتح
    echo - صف إجماليات مميز
    echo - حدود وتنسيق احترافي
    echo.
) else (
    echo ❌ فشل في بناء المشروع
    echo تحقق من الأخطاء أعلاه
)

echo.
echo اضغط أي مفتاح للخروج...
pause > nul