using System;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Dialogs
{
    public partial class AddCustomerDialog : Window
    {
        public Customer Customer { get; private set; }
        public bool IsSuccess { get; private set; }

        public AddCustomerDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        public AddCustomerDialog(Customer customer) : this()
        {
            if (customer != null)
            {
                LoadCustomerData(customer);
                Title = "تعديل بيانات العميل";
            }
        }

        private void InitializeDialog()
        {
            // تعيين التاريخ الافتراضي
            dpOpeningBalanceDate.SelectedDate = DateTime.Now;
            
            // تركيز على حقل الاسم
            txtCustomerName.Focus();
        }

        private void LoadCustomerData(Customer customer)
        {
            txtCustomerCode.Text = customer.Code;
            txtCustomerName.Text = customer.Name;
            txtContactPerson.Text = customer.ContactPerson;
            txtPhone.Text = customer.Phone;
            txtEmail.Text = customer.Email;
            txtAddress.Text = customer.Address;
            txtTaxNumber.Text = customer.TaxNumber;
            txtCreditLimit.Text = customer.CreditLimit.ToString();
            txtOpeningBalance.Text = customer.OpeningBalance.ToString();
            dpOpeningBalanceDate.SelectedDate = customer.OpeningBalanceDate;
            txtNotes.Text = customer.Notes;
            chkIsActive.IsChecked = customer.IsActive;
        }

        private bool ValidateInput()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtCustomerName.Focus();
                return false;
            }

            // التحقق من صحة البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(txtEmail.Text);
                    if (addr.Address != txtEmail.Text)
                    {
                        MessageBox.Show("البريد الإلكتروني غير صحيح", "خطأ في البيانات", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        txtEmail.Focus();
                        return false;
                    }
                }
                catch
                {
                    MessageBox.Show("البريد الإلكتروني غير صحيح", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtEmail.Focus();
                    return false;
                }
            }

            // التحقق من الأرقام
            if (!string.IsNullOrWhiteSpace(txtCreditLimit.Text))
            {
                if (!decimal.TryParse(txtCreditLimit.Text, out _))
                {
                    MessageBox.Show("حد الائتمان يجب أن يكون رقماً صحيحاً", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCreditLimit.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(txtOpeningBalance.Text))
            {
                if (!decimal.TryParse(txtOpeningBalance.Text, out _))
                {
                    MessageBox.Show("الرصيد الافتتاحي يجب أن يكون رقماً صحيحاً", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtOpeningBalance.Focus();
                    return false;
                }
            }

            return true;
        }

        private Customer CreateCustomerFromInput()
        {
            var customer = new Customer
            {
                Code = txtCustomerCode.Text?.Trim(),
                Name = txtCustomerName.Text?.Trim(),
                ContactPerson = txtContactPerson.Text?.Trim(),
                Phone = txtPhone.Text?.Trim(),
                Email = txtEmail.Text?.Trim(),
                Address = txtAddress.Text?.Trim(),
                TaxNumber = txtTaxNumber.Text?.Trim(),
                Notes = txtNotes.Text?.Trim(),
                IsActive = chkIsActive.IsChecked ?? true,
                OpeningBalanceDate = dpOpeningBalanceDate.SelectedDate ?? DateTime.Now,
                CreatedAt = DateTime.Now
            };

            // تحويل الأرقام
            if (decimal.TryParse(txtCreditLimit.Text, out decimal creditLimit))
                customer.CreditLimit = creditLimit;

            if (decimal.TryParse(txtOpeningBalance.Text, out decimal openingBalance))
                customer.OpeningBalance = openingBalance;

            return customer;
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                Customer = CreateCustomerFromInput();
                IsSuccess = true;
                
                MessageBox.Show("تم حفظ بيانات العميل بنجاح", "نجح الحفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            IsSuccess = false;
            DialogResult = false;
            Close();
        }
    }
}
