<UserControl x:Class="InjazAcc.UI.Views.Settings.AdvancedColorControlPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:InjazAcc.UI.Views.Settings"
             Background="{DynamicResource BackgroundBrush}">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="التحكم المتقدم بالألوان" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="{DynamicResource PrimaryTextBrush}"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="تحكم في كل لون في البرنامج بشكل منفصل" 
                          FontSize="14" 
                          Foreground="{DynamicResource SecondaryTextBrush}"
                          HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>

            <!-- المحتوى الرئيسي -->
            <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                
                <!-- تبويب الألوان الأساسية -->
                <TabItem Header="الألوان الأساسية">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- الألوان الرئيسية -->
                            <GroupBox Header="الألوان الرئيسية" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- اللون الأساسي -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="اللون الأساسي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnPrimaryColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource PrimaryBrush}" Tag="PrimaryColor"/>
                                        <TextBox x:Name="txtPrimaryColor" Margin="0,5,0,0" 
                                                Text="{Binding PrimaryColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="PrimaryColor"/>
                                    </StackPanel>

                                    <!-- اللون الأساسي الداكن -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="اللون الأساسي الداكن" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnPrimaryDarkColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource PrimaryDarkBrush}" Tag="PrimaryDarkColor"/>
                                        <TextBox x:Name="txtPrimaryDarkColor" Margin="0,5,0,0" 
                                                Text="{Binding PrimaryDarkColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="PrimaryDarkColor"/>
                                    </StackPanel>

                                    <!-- اللون الأساسي الفاتح -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="اللون الأساسي الفاتح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnPrimaryLightColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource PrimaryLightBrush}" Tag="PrimaryLightColor"/>
                                        <TextBox x:Name="txtPrimaryLightColor" Margin="0,5,0,0" 
                                                Text="{Binding PrimaryLightColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="PrimaryLightColor"/>
                                    </StackPanel>

                                    <!-- اللون الثانوي -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="اللون الثانوي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryBrush}" Tag="SecondaryColor"/>
                                        <TextBox x:Name="txtSecondaryColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryColor"/>
                                    </StackPanel>

                                    <!-- اللون الثانوي الداكن -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="اللون الثانوي الداكن" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryDarkColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryDarkBrush}" Tag="SecondaryDarkColor"/>
                                        <TextBox x:Name="txtSecondaryDarkColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryDarkColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryDarkColor"/>
                                    </StackPanel>

                                    <!-- اللون الثانوي الفاتح -->
                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="اللون الثانوي الفاتح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryLightColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryLightBrush}" Tag="SecondaryLightColor"/>
                                        <TextBox x:Name="txtSecondaryLightColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryLightColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryLightColor"/>
                                    </StackPanel>

                                    <!-- لون التمييز -->
                                    <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون التمييز" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnAccentColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource AccentBrush}" Tag="AccentColor"/>
                                        <TextBox x:Name="txtAccentColor" Margin="0,5,0,0" 
                                                Text="{Binding AccentColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="AccentColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب ألوان الخلفية -->
                <TabItem Header="ألوان الخلفية">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <GroupBox Header="ألوان الخلفية" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- خلفية التطبيق -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="خلفية التطبيق" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource BackgroundBrush}" Tag="BackgroundColor"/>
                                        <TextBox x:Name="txtBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding BackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="BackgroundColor"/>
                                    </StackPanel>

                                    <!-- خلفية السطح -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="خلفية السطح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSurfaceColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SurfaceBrush}" Tag="SurfaceColor"/>
                                        <TextBox x:Name="txtSurfaceColor" Margin="0,5,0,0" 
                                                Text="{Binding SurfaceColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SurfaceColor"/>
                                    </StackPanel>

                                    <!-- خلفية البطاقات -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="خلفية البطاقات" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnCardBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource CardBackgroundBrush}" Tag="CardBackgroundColor"/>
                                        <TextBox x:Name="txtCardBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding CardBackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="CardBackgroundColor"/>
                                    </StackPanel>

                                    <!-- خلفية الحوارات -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="خلفية الحوارات" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnDialogBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource DialogBackgroundBrush}" Tag="DialogBackgroundColor"/>
                                        <TextBox x:Name="txtDialogBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding DialogBackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="DialogBackgroundColor"/>
                                    </StackPanel>

                                    <!-- خلفية الشريط الجانبي -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="خلفية الشريط الجانبي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSidebarBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SidebarBackgroundBrush}" Tag="SidebarBackgroundColor"/>
                                        <TextBox x:Name="txtSidebarBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding SidebarBackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SidebarBackgroundColor"/>
                                    </StackPanel>

                                    <!-- خلفية الرأس -->
                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="خلفية الرأس" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnHeaderBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource HeaderBackgroundBrush}" Tag="HeaderBackgroundColor"/>
                                        <TextBox x:Name="txtHeaderBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding HeaderBackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="HeaderBackgroundColor"/>
                                    </StackPanel>

                                    <!-- خلفية التذييل -->
                                    <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                        <TextBlock Text="خلفية التذييل" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnFooterBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource FooterBackgroundBrush}" Tag="FooterBackgroundColor"/>
                                        <TextBox x:Name="txtFooterBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding FooterBackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="FooterBackgroundColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب ألوان النصوص -->
                <TabItem Header="ألوان النصوص">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <GroupBox Header="ألوان النصوص" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- النص الأساسي -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="النص الأساسي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnPrimaryTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource PrimaryTextBrush}" Tag="PrimaryTextColor"/>
                                        <TextBox x:Name="txtPrimaryTextColor" Margin="0,5,0,0" 
                                                Text="{Binding PrimaryTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="PrimaryTextColor"/>
                                    </StackPanel>

                                    <!-- النص الثانوي -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="النص الثانوي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryTextBrush}" Tag="SecondaryTextColor"/>
                                        <TextBox x:Name="txtSecondaryTextColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryTextColor"/>
                                    </StackPanel>

                                    <!-- النص المعطل -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="النص المعطل" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnDisabledTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource DisabledTextBrush}" Tag="DisabledTextColor"/>
                                        <TextBox x:Name="txtDisabledTextColor" Margin="0,5,0,0" 
                                                Text="{Binding DisabledTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="DisabledTextColor"/>
                                    </StackPanel>

                                    <!-- نص التلميح -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="نص التلميح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnHintTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource HintTextBrush}" Tag="HintTextColor"/>
                                        <TextBox x:Name="txtHintTextColor" Margin="0,5,0,0" 
                                                Text="{Binding HintTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="HintTextColor"/>
                                    </StackPanel>

                                    <!-- النص على الأساسي -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="النص على الأساسي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnOnPrimaryTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource OnPrimaryTextBrush}" Tag="OnPrimaryTextColor"/>
                                        <TextBox x:Name="txtOnPrimaryTextColor" Margin="0,5,0,0" 
                                                Text="{Binding OnPrimaryTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="OnPrimaryTextColor"/>
                                    </StackPanel>

                                    <!-- النص على الثانوي -->
                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="النص على الثانوي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnOnSecondaryTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource OnSecondaryTextBrush}" Tag="OnSecondaryTextColor"/>
                                        <TextBox x:Name="txtOnSecondaryTextColor" Margin="0,5,0,0" 
                                                Text="{Binding OnSecondaryTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="OnSecondaryTextColor"/>
                                    </StackPanel>

                                    <!-- النص على السطح -->
                                    <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                        <TextBlock Text="النص على السطح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnOnSurfaceTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource OnSurfaceTextBrush}" Tag="OnSurfaceTextColor"/>
                                        <TextBox x:Name="txtOnSurfaceTextColor" Margin="0,5,0,0" 
                                                Text="{Binding OnSurfaceTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="OnSurfaceTextColor"/>
                                    </StackPanel>

                                    <!-- النص على الخلفية -->
                                    <StackPanel Grid.Row="2" Grid.Column="1" Margin="5">
                                        <TextBlock Text="النص على الخلفية" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnOnBackgroundTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource OnBackgroundTextBrush}" Tag="OnBackgroundTextColor"/>
                                        <TextBox x:Name="txtOnBackgroundTextColor" Margin="0,5,0,0" 
                                                Text="{Binding OnBackgroundTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="OnBackgroundTextColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب ألوان الحدود -->
                <TabItem Header="ألوان الحدود">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <GroupBox Header="ألوان الحدود والخطوط" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- لون الحدود -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون الحدود" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnBorderColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource BorderBrush}" Tag="BorderColor"/>
                                        <TextBox x:Name="txtBorderColor" Margin="0,5,0,0" 
                                                Text="{Binding BorderColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="BorderColor"/>
                                    </StackPanel>

                                    <!-- لون الفاصل -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="لون الفاصل" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnDividerColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource DividerBrush}" Tag="DividerColor"/>
                                        <TextBox x:Name="txtDividerColor" Margin="0,5,0,0" 
                                                Text="{Binding DividerColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="DividerColor"/>
                                    </StackPanel>

                                    <!-- لون المخطط -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="لون المخطط" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnOutlineColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource OutlineBrush}" Tag="OutlineColor"/>
                                        <TextBox x:Name="txtOutlineColor" Margin="0,5,0,0" 
                                                Text="{Binding OutlineColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="OutlineColor"/>
                                    </StackPanel>

                                    <!-- لون حدود التركيز -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون حدود التركيز" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnFocusBorderColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource FocusBorderBrush}" Tag="FocusBorderColor"/>
                                        <TextBox x:Name="txtFocusBorderColor" Margin="0,5,0,0" 
                                                Text="{Binding FocusBorderColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="FocusBorderColor"/>
                                    </StackPanel>

                                    <!-- لون حدود الخطأ -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="لون حدود الخطأ" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnErrorBorderColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ErrorBorderBrush}" Tag="ErrorBorderColor"/>
                                        <TextBox x:Name="txtErrorBorderColor" Margin="0,5,0,0" 
                                                Text="{Binding ErrorBorderColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ErrorBorderColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب ألوان الأزرار -->
                <TabItem Header="ألوان الأزرار">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- الأزرار الأساسية -->
                            <GroupBox Header="الأزرار الأساسية" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- خلفية الزر -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="خلفية الزر" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnButtonBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ButtonBackgroundBrush}" Tag="ButtonBackgroundColor"/>
                                        <TextBox x:Name="txtButtonBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding ButtonBackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ButtonBackgroundColor"/>
                                    </StackPanel>

                                    <!-- نص الزر -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="نص الزر" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnButtonTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ButtonTextBrush}" Tag="ButtonTextColor"/>
                                        <TextBox x:Name="txtButtonTextColor" Margin="0,5,0,0" 
                                                Text="{Binding ButtonTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ButtonTextColor"/>
                                    </StackPanel>

                                    <!-- الزر عند التمرير -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="الزر عند التمرير" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnButtonHoverColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ButtonHoverBrush}" Tag="ButtonHoverColor"/>
                                        <TextBox x:Name="txtButtonHoverColor" Margin="0,5,0,0" 
                                                Text="{Binding ButtonHoverColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ButtonHoverColor"/>
                                    </StackPanel>

                                    <!-- الزر عند الضغط -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="الزر عند الضغط" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnButtonPressedColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ButtonPressedBrush}" Tag="ButtonPressedColor"/>
                                        <TextBox x:Name="txtButtonPressedColor" Margin="0,5,0,0" 
                                                Text="{Binding ButtonPressedColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ButtonPressedColor"/>
                                    </StackPanel>

                                    <!-- الزر المعطل -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="الزر المعطل" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnButtonDisabledColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ButtonDisabledBrush}" Tag="ButtonDisabledColor"/>
                                        <TextBox x:Name="txtButtonDisabledColor" Margin="0,5,0,0" 
                                                Text="{Binding ButtonDisabledColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ButtonDisabledColor"/>
                                    </StackPanel>

                                    <!-- نص الزر المعطل -->
                                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                        <TextBlock Text="نص الزر المعطل" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnButtonDisabledTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ButtonDisabledTextBrush}" Tag="ButtonDisabledTextColor"/>
                                        <TextBox x:Name="txtButtonDisabledTextColor" Margin="0,5,0,0" 
                                                Text="{Binding ButtonDisabledTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ButtonDisabledTextColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                            <!-- الأزرار الثانوية -->
                            <GroupBox Header="الأزرار الثانوية" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- خلفية الزر الثانوي -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="خلفية الزر الثانوي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryButtonBackgroundColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryButtonBackgroundBrush}" Tag="SecondaryButtonBackgroundColor"/>
                                        <TextBox x:Name="txtSecondaryButtonBackgroundColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryButtonBackgroundColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryButtonBackgroundColor"/>
                                    </StackPanel>

                                    <!-- نص الزر الثانوي -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="نص الزر الثانوي" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryButtonTextColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryButtonTextBrush}" Tag="SecondaryButtonTextColor"/>
                                        <TextBox x:Name="txtSecondaryButtonTextColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryButtonTextColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryButtonTextColor"/>
                                    </StackPanel>

                                    <!-- الزر الثانوي عند التمرير -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="الزر الثانوي عند التمرير" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryButtonHoverColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryButtonHoverBrush}" Tag="SecondaryButtonHoverColor"/>
                                        <TextBox x:Name="txtSecondaryButtonHoverColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryButtonHoverColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryButtonHoverColor"/>
                                    </StackPanel>

                                    <!-- الزر الثانوي عند الضغط -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="الزر الثانوي عند الضغط" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSecondaryButtonPressedColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SecondaryButtonPressedBrush}" Tag="SecondaryButtonPressedColor"/>
                                        <TextBox x:Name="txtSecondaryButtonPressedColor" Margin="0,5,0,0" 
                                                Text="{Binding SecondaryButtonPressedColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SecondaryButtonPressedColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب ألوان الحالة -->
                <TabItem Header="ألوان الحالة">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <GroupBox Header="ألوان الحالة والتفاعل" Margin="0,0,0,20">
                                <Grid Margin="10">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <!-- لون التحديد -->
                                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون التحديد" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSelectionColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SelectionBrush}" Tag="SelectionColor"/>
                                        <TextBox x:Name="txtSelectionColor" Margin="0,5,0,0" 
                                                Text="{Binding SelectionColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SelectionColor"/>
                                    </StackPanel>

                                    <!-- لون التمرير -->
                                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                        <TextBlock Text="لون التمرير" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnHoverColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource HoverBrush}" Tag="HoverColor"/>
                                        <TextBox x:Name="txtHoverColor" Margin="0,5,0,0" 
                                                Text="{Binding HoverColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="HoverColor"/>
                                    </StackPanel>

                                    <!-- لون التركيز -->
                                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                        <TextBlock Text="لون التركيز" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnFocusColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource FocusBrush}" Tag="FocusColor"/>
                                        <TextBox x:Name="txtFocusColor" Margin="0,5,0,0" 
                                                Text="{Binding FocusColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="FocusColor"/>
                                    </StackPanel>

                                    <!-- لون النشط -->
                                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون النشط" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnActiveColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ActiveBrush}" Tag="ActiveColor"/>
                                        <TextBox x:Name="txtActiveColor" Margin="0,5,0,0" 
                                                Text="{Binding ActiveColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ActiveColor"/>
                                    </StackPanel>

                                    <!-- لون التمييز -->
                                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                        <TextBlock Text="لون التمييز" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnHighlightColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource HighlightBrush}" Tag="HighlightColor"/>
                                        <TextBox x:Name="txtHighlightColor" Margin="0,5,0,0" 
                                                Text="{Binding HighlightColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="HighlightColor"/>
                                    </StackPanel>

                                    <!-- لون النجاح -->
                                    <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون النجاح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSuccessColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SuccessBrush}" Tag="SuccessColor"/>
                                        <TextBox x:Name="txtSuccessColor" Margin="0,5,0,0" 
                                                Text="{Binding SuccessColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SuccessColor"/>
                                    </StackPanel>

                                    <!-- لون النجاح الفاتح -->
                                    <StackPanel Grid.Row="2" Grid.Column="1" Margin="5">
                                        <TextBlock Text="لون النجاح الفاتح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSuccessLightColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SuccessLightBrush}" Tag="SuccessLightColor"/>
                                        <TextBox x:Name="txtSuccessLightColor" Margin="0,5,0,0" 
                                                Text="{Binding SuccessLightColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SuccessLightColor"/>
                                    </StackPanel>

                                    <!-- لون النجاح الداكن -->
                                    <StackPanel Grid.Row="2" Grid.Column="2" Margin="5">
                                        <TextBlock Text="لون النجاح الداكن" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnSuccessDarkColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource SuccessDarkBrush}" Tag="SuccessDarkColor"/>
                                        <TextBox x:Name="txtSuccessDarkColor" Margin="0,5,0,0" 
                                                Text="{Binding SuccessDarkColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="SuccessDarkColor"/>
                                    </StackPanel>

                                    <!-- لون التحذير -->
                                    <StackPanel Grid.Row="3" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون التحذير" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnWarningColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource WarningBrush}" Tag="WarningColor"/>
                                        <TextBox x:Name="txtWarningColor" Margin="0,5,0,0" 
                                                Text="{Binding WarningColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="WarningColor"/>
                                    </StackPanel>

                                    <!-- لون التحذير الفاتح -->
                                    <StackPanel Grid.Row="3" Grid.Column="1" Margin="5">
                                        <TextBlock Text="لون التحذير الفاتح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnWarningLightColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource WarningLightBrush}" Tag="WarningLightColor"/>
                                        <TextBox x:Name="txtWarningLightColor" Margin="0,5,0,0" 
                                                Text="{Binding WarningLightColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="WarningLightColor"/>
                                    </StackPanel>

                                    <!-- لون التحذير الداكن -->
                                    <StackPanel Grid.Row="3" Grid.Column="2" Margin="5">
                                        <TextBlock Text="لون التحذير الداكن" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnWarningDarkColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource WarningDarkBrush}" Tag="WarningDarkColor"/>
                                        <TextBox x:Name="txtWarningDarkColor" Margin="0,5,0,0" 
                                                Text="{Binding WarningDarkColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="WarningDarkColor"/>
                                    </StackPanel>

                                    <!-- لون الخطأ -->
                                    <StackPanel Grid.Row="4" Grid.Column="0" Margin="5">
                                        <TextBlock Text="لون الخطأ" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnErrorColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ErrorBrush}" Tag="ErrorColor"/>
                                        <TextBox x:Name="txtErrorColor" Margin="0,5,0,0" 
                                                Text="{Binding ErrorColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ErrorColor"/>
                                    </StackPanel>

                                    <!-- لون الخطأ الفاتح -->
                                    <StackPanel Grid.Row="4" Grid.Column="1" Margin="5">
                                        <TextBlock Text="لون الخطأ الفاتح" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnErrorLightColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ErrorLightBrush}" Tag="ErrorLightColor"/>
                                        <TextBox x:Name="txtErrorLightColor" Margin="0,5,0,0" 
                                                Text="{Binding ErrorLightColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ErrorLightColor"/>
                                    </StackPanel>

                                    <!-- لون الخطأ الداكن -->
                                    <StackPanel Grid.Row="4" Grid.Column="2" Margin="5">
                                        <TextBlock Text="لون الخطأ الداكن" FontWeight="Bold" Margin="0,0,0,5"/>
                                        <Button x:Name="btnErrorDarkColor" Height="40" Click="ColorButton_Click" 
                                               Background="{DynamicResource ErrorDarkBrush}" Tag="ErrorDarkColor"/>
                                        <TextBox x:Name="txtErrorDarkColor" Margin="0,5,0,0" 
                                                Text="{Binding ErrorDarkColor, UpdateSourceTrigger=PropertyChanged}"
                                                TextChanged="ColorTextBox_TextChanged" Tag="ErrorDarkColor"/>
                                    </StackPanel>
                                </Grid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب الجداول والرسوم البيانية -->
                <TabItem Header="الجداول والرسوم البيانية">
                    <local:TableAndChartColorsPage/>
                </TabItem>

                <!-- تبويب القوالب والأدوات -->
                <TabItem Header="القوالب والأدوات">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- القوالب الجاهزة -->
                            <GroupBox Header="القوالب الجاهزة" Margin="0,0,0,20">
                                <WrapPanel Margin="10" Orientation="Horizontal">
                                    <Button Content="افتراضي" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="default"/>
                                    <Button Content="داكن" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="dark"/>
                                    <Button Content="أزرق" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="blue"/>
                                    <Button Content="أخضر" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="green"/>
                                    <Button Content="بنفسجي" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="purple"/>
                                    <Button Content="برتقالي" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="orange"/>
                                    <Button Content="أحمر" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="red"/>
                                    <Button Content="تباين عالي" Margin="5" Padding="15,10" Click="ApplyTheme_Click" Tag="highcontrast"/>
                                </WrapPanel>
                            </GroupBox>

                            <!-- أدوات الألوان -->
                            <GroupBox Header="أدوات الألوان" Margin="0,0,0,20">
                                <StackPanel Margin="10">
                                    <WrapPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <Button Content="حفظ الإعدادات" Margin="5" Padding="15,10" Click="SaveSettings_Click"/>
                                        <Button Content="تحميل الإعدادات" Margin="5" Padding="15,10" Click="LoadSettings_Click"/>
                                        <Button Content="إعادة تعيين" Margin="5" Padding="15,10" Click="ResetSettings_Click"/>
                                        <Button Content="تصدير الألوان" Margin="5" Padding="15,10" Click="ExportColors_Click"/>
                                        <Button Content="استيراد الألوان" Margin="5" Padding="15,10" Click="ImportColors_Click"/>
                                    </WrapPanel>
                                    
                                    <!-- معاينة الألوان -->
                                    <Border Height="100" CornerRadius="5" Margin="0,10,0,0" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                <GradientStop Color="{DynamicResource PrimaryColor}" Offset="0"/>
                                                <GradientStop Color="{DynamicResource SecondaryColor}" Offset="0.5"/>
                                                <GradientStop Color="{DynamicResource AccentColor}" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>
                                        <TextBlock Text="معاينة الألوان الحالية" 
                                                  HorizontalAlignment="Center" VerticalAlignment="Center" 
                                                  FontSize="16" FontWeight="Bold" 
                                                  Foreground="{DynamicResource OnPrimaryTextBrush}"/>
                                    </Border>
                                </StackPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

            </TabControl>

        </Grid>
    </ScrollViewer>

</UserControl>