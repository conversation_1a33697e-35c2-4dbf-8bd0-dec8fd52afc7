using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;

namespace InjazAcc.UI.Views.Shared
{
    /// <summary>
    /// نافذة منتقي الألوان المتقدمة
    /// </summary>
    public partial class AdvancedColorPickerWindow : Window
    {
        private Color _selectedColor = Colors.Red;
        private Color _originalColor = Colors.Red;
        private bool _isUpdatingFromCode = false;
        private List<Color> _savedColors = new List<Color>();
        private readonly string _savedColorsPath;

        // خصائص HSV
        private double _hue = 0;
        private double _saturation = 1;
        private double _value = 1;
        private double _alpha = 1;

        public Color SelectedColor
        {
            get => _selectedColor;
            set
            {
                _selectedColor = value;
                _originalColor = value;
                UpdateFromColor(value);
            }
        }

        public Color OriginalColor
        {
            get => _originalColor;
            set => _originalColor = value;
        }

        public AdvancedColorPickerWindow()
        {
            InitializeComponent();
            
            _savedColorsPath = System.IO.Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "InjazAcc", "SavedColors.json");

            InitializeColorWheel();
            InitializeBrightnessBar();
            InitializeAlphaBar();
            InitializeQuickColors();
            LoadSavedColors();
            
            UpdateFromColor(_selectedColor);
        }

        /// <summary>
        /// تهيئة عجلة الألوان
        /// </summary>
        private void InitializeColorWheel()
        {
            try
            {
                colorWheel.Children.Clear();
                
                double centerX = 150;
                double centerY = 150;
                double radius = 140;

                // رسم عجلة الألوان
                for (int angle = 0; angle < 360; angle += 2)
                {
                    for (int r = 0; r < radius; r += 2)
                    {
                        double hue = angle;
                        double saturation = (double)r / radius;
                        
                        var color = HsvToRgb(hue, saturation, 1.0);
                        var brush = new SolidColorBrush(color);
                        
                        var rect = new Rectangle
                        {
                            Width = 3,
                            Height = 3,
                            Fill = brush
                        };
                        
                        double x = centerX + r * Math.Cos(angle * Math.PI / 180);
                        double y = centerY + r * Math.Sin(angle * Math.PI / 180);
                        
                        Canvas.SetLeft(rect, x);
                        Canvas.SetTop(rect, y);
                        
                        colorWheel.Children.Add(rect);
                    }
                }

                // إضافة مؤشر التحديد
                var selector = new Ellipse
                {
                    Width = 10,
                    Height = 10,
                    Stroke = Brushes.White,
                    StrokeThickness = 2,
                    Fill = Brushes.Transparent
                };
                
                colorWheel.Children.Add(selector);
                colorWheel.Tag = selector; // حفظ مرجع للمؤشر
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة عجلة الألوان: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة شريط السطوع
        /// </summary>
        private void InitializeBrightnessBar()
        {
            try
            {
                brightnessBar.Children.Clear();
                
                var gradient = new LinearGradientBrush();
                gradient.StartPoint = new Point(0, 0);
                gradient.EndPoint = new Point(1, 0);
                
                gradient.GradientStops.Add(new GradientStop(Colors.Black, 0));
                gradient.GradientStops.Add(new GradientStop(Colors.White, 1));
                
                var rect = new Rectangle
                {
                    Width = brightnessBar.Width,
                    Height = 30,
                    Fill = gradient
                };
                
                brightnessBar.Children.Add(rect);
                
                // إضافة مؤشر
                var selector = new Rectangle
                {
                    Width = 3,
                    Height = 30,
                    Fill = Brushes.White,
                    Stroke = Brushes.Black,
                    StrokeThickness = 1
                };
                
                brightnessBar.Children.Add(selector);
                brightnessBar.Tag = selector;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة شريط السطوع: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة شريط الشفافية
        /// </summary>
        private void InitializeAlphaBar()
        {
            try
            {
                alphaBar.Children.Clear();
                
                // خلفية شطرنجية للشفافية
                var checkerboard = CreateCheckerboardBrush();
                var backgroundRect = new Rectangle
                {
                    Width = alphaBar.Width,
                    Height = 30,
                    Fill = checkerboard
                };
                alphaBar.Children.Add(backgroundRect);
                
                // تدرج الشفافية
                var gradient = new LinearGradientBrush();
                gradient.StartPoint = new Point(0, 0);
                gradient.EndPoint = new Point(1, 0);
                
                var transparentColor = _selectedColor;
                transparentColor.A = 0;
                var opaqueColor = _selectedColor;
                opaqueColor.A = 255;
                
                gradient.GradientStops.Add(new GradientStop(transparentColor, 0));
                gradient.GradientStops.Add(new GradientStop(opaqueColor, 1));
                
                var gradientRect = new Rectangle
                {
                    Width = alphaBar.Width,
                    Height = 30,
                    Fill = gradient
                };
                
                alphaBar.Children.Add(gradientRect);
                
                // إضافة مؤشر
                var selector = new Rectangle
                {
                    Width = 3,
                    Height = 30,
                    Fill = Brushes.White,
                    Stroke = Brushes.Black,
                    StrokeThickness = 1
                };
                
                alphaBar.Children.Add(selector);
                alphaBar.Tag = selector;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة شريط الشفافية: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة الألوان السريعة
        /// </summary>
        private void InitializeQuickColors()
        {
            try
            {
                var quickColors = new Color[]
                {
                    Colors.Red, Colors.Green, Colors.Blue, Colors.Yellow, Colors.Orange, Colors.Purple,
                    Colors.Pink, Colors.Cyan, Colors.Lime, Colors.Magenta, Colors.Brown, Colors.Gray,
                    Colors.Black, Colors.White, Colors.Silver, Colors.Gold, Colors.Navy, Colors.Maroon,
                    Colors.Olive, Colors.Teal, Colors.Aqua, Colors.Fuchsia, Colors.DarkRed, Colors.DarkGreen,
                    Colors.DarkBlue, Colors.DarkOrange, Colors.DarkViolet, Colors.DarkCyan, Colors.LightGray, Colors.DarkGray
                };

                foreach (var color in quickColors)
                {
                    var button = new Button
                    {
                        Width = 25,
                        Height = 25,
                        Margin = new Thickness(2),
                        Background = new SolidColorBrush(color),
                        BorderBrush = Brushes.Gray,
                        BorderThickness = new Thickness(1),
                        Tag = color
                    };
                    
                    button.Click += QuickColor_Click;
                    quickColorsPanel.Children.Add(button);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة الألوان السريعة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل الألوان المحفوظة
        /// </summary>
        private void LoadSavedColors()
        {
            try
            {
                if (File.Exists(_savedColorsPath))
                {
                    var json = File.ReadAllText(_savedColorsPath);
                    var colorStrings = JsonSerializer.Deserialize<List<string>>(json);
                    
                    if (colorStrings != null)
                    {
                        _savedColors = colorStrings.Select(c => (Color)ColorConverter.ConvertFromString(c)).ToList();
                        UpdateSavedColorsPanel();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الألوان المحفوظة: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ الألوان المحفوظة
        /// </summary>
        private void SaveSavedColors()
        {
            try
            {
                var directory = System.IO.Path.GetDirectoryName(_savedColorsPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var colorStrings = _savedColors.Select(c => c.ToString()).ToList();
                var json = JsonSerializer.Serialize(colorStrings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_savedColorsPath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الألوان المحفوظة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث لوحة الألوان المحفوظة
        /// </summary>
        private void UpdateSavedColorsPanel()
        {
            try
            {
                savedColorsPanel.Children.Clear();
                
                foreach (var color in _savedColors)
                {
                    var button = new Button
                    {
                        Width = 25,
                        Height = 25,
                        Margin = new Thickness(2),
                        Background = new SolidColorBrush(color),
                        BorderBrush = Brushes.Gray,
                        BorderThickness = new Thickness(1),
                        Tag = color
                    };
                    
                    button.Click += SavedColor_Click;
                    button.MouseRightButtonDown += SavedColor_RightClick;
                    savedColorsPanel.Children.Add(button);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث لوحة الألوان المحفوظة: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث من لون
        /// </summary>
        private void UpdateFromColor(Color color)
        {
            try
            {
                _isUpdatingFromCode = true;
                
                _selectedColor = color;
                RgbToHsv(color.R, color.G, color.B, out _hue, out _saturation, out _value);
                _alpha = color.A / 255.0;
                
                // تحديث مربعات النص
                txtRed.Text = color.R.ToString();
                txtGreen.Text = color.G.ToString();
                txtBlue.Text = color.B.ToString();
                
                txtHue.Text = Math.Round(_hue).ToString();
                txtSaturation.Text = Math.Round(_saturation * 100).ToString();
                txtValue.Text = Math.Round(_value * 100).ToString();
                
                txtHexColor.Text = color.ToString();
                
                // تحديث معاينة اللون
                currentColorPreview.Background = new SolidColorBrush(color);
                
                // تحديث المؤشرات
                UpdateColorWheelSelector();
                UpdateBrightnessBarSelector();
                UpdateAlphaBarSelector();
                
                _isUpdatingFromCode = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث من اللون: {ex.Message}");
                _isUpdatingFromCode = false;
            }
        }

        /// <summary>
        /// تحديث مؤشر عجلة الألوان
        /// </summary>
        private void UpdateColorWheelSelector()
        {
            try
            {
                if (colorWheel.Tag is Ellipse selector)
                {
                    double centerX = 150;
                    double centerY = 150;
                    double radius = _saturation * 140;
                    
                    double x = centerX + radius * Math.Cos(_hue * Math.PI / 180) - 5;
                    double y = centerY + radius * Math.Sin(_hue * Math.PI / 180) - 5;
                    
                    Canvas.SetLeft(selector, x);
                    Canvas.SetTop(selector, y);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مؤشر عجلة الألوان: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مؤشر شريط السطوع
        /// </summary>
        private void UpdateBrightnessBarSelector()
        {
            try
            {
                if (brightnessBar.Tag is Rectangle selector)
                {
                    double x = _value * (brightnessBar.ActualWidth - 3);
                    Canvas.SetLeft(selector, x);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مؤشر شريط السطوع: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مؤشر شريط الشفافية
        /// </summary>
        private void UpdateAlphaBarSelector()
        {
            try
            {
                if (alphaBar.Tag is Rectangle selector)
                {
                    double x = _alpha * (alphaBar.ActualWidth - 3);
                    Canvas.SetLeft(selector, x);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مؤشر شريط الشفافية: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء فرشاة شطرنجية للشفافية
        /// </summary>
        private DrawingBrush CreateCheckerboardBrush()
        {
            var brush = new DrawingBrush();
            brush.TileMode = TileMode.Tile;
            brush.Viewport = new Rect(0, 0, 10, 10);
            brush.ViewportUnits = BrushMappingMode.Absolute;
            
            var drawing = new DrawingGroup();
            drawing.Children.Add(new GeometryDrawing(Brushes.White, null, new RectangleGeometry(new Rect(0, 0, 10, 10))));
            drawing.Children.Add(new GeometryDrawing(Brushes.LightGray, null, new RectangleGeometry(new Rect(0, 0, 5, 5))));
            drawing.Children.Add(new GeometryDrawing(Brushes.LightGray, null, new RectangleGeometry(new Rect(5, 5, 5, 5))));
            
            brush.Drawing = drawing;
            return brush;
        }

        /// <summary>
        /// تحويل HSV إلى RGB
        /// </summary>
        private Color HsvToRgb(double hue, double saturation, double value)
        {
            int hi = Convert.ToInt32(Math.Floor(hue / 60)) % 6;
            double f = hue / 60 - Math.Floor(hue / 60);

            value = value * 255;
            int v = Convert.ToInt32(value);
            int p = Convert.ToInt32(value * (1 - saturation));
            int q = Convert.ToInt32(value * (1 - f * saturation));
            int t = Convert.ToInt32(value * (1 - (1 - f) * saturation));

            return hi switch
            {
                0 => Color.FromArgb((byte)(_alpha * 255), (byte)v, (byte)t, (byte)p),
                1 => Color.FromArgb((byte)(_alpha * 255), (byte)q, (byte)v, (byte)p),
                2 => Color.FromArgb((byte)(_alpha * 255), (byte)p, (byte)v, (byte)t),
                3 => Color.FromArgb((byte)(_alpha * 255), (byte)p, (byte)q, (byte)v),
                4 => Color.FromArgb((byte)(_alpha * 255), (byte)t, (byte)p, (byte)v),
                _ => Color.FromArgb((byte)(_alpha * 255), (byte)v, (byte)p, (byte)q),
            };
        }

        /// <summary>
        /// تحويل RGB إلى HSV
        /// </summary>
        private void RgbToHsv(int red, int green, int blue, out double hue, out double saturation, out double value)
        {
            double r = red / 255.0;
            double g = green / 255.0;
            double b = blue / 255.0;

            double max = Math.Max(r, Math.Max(g, b));
            double min = Math.Min(r, Math.Min(g, b));
            double delta = max - min;

            // Hue
            if (delta == 0)
                hue = 0;
            else if (max == r)
                hue = 60 * (((g - b) / delta) % 6);
            else if (max == g)
                hue = 60 * (((b - r) / delta) + 2);
            else
                hue = 60 * (((r - g) / delta) + 4);

            if (hue < 0) hue += 360;

            // Saturation
            saturation = max == 0 ? 0 : delta / max;

            // Value
            value = max;
        }

        // معالجات الأحداث
        private void ColorWheel_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Canvas canvas)
            {
                canvas.CaptureMouse();
                UpdateColorFromWheel(e.GetPosition(canvas));
            }
        }

        private void ColorWheel_MouseMove(object sender, MouseEventArgs e)
        {
            if (sender is Canvas canvas && canvas.IsMouseCaptured)
            {
                UpdateColorFromWheel(e.GetPosition(canvas));
            }
        }

        private void UpdateColorFromWheel(Point position)
        {
            try
            {
                double centerX = 150;
                double centerY = 150;
                double maxRadius = 140;

                double deltaX = position.X - centerX;
                double deltaY = position.Y - centerY;
                double distance = Math.Sqrt(deltaX * deltaX + deltaY * deltaY);
                
                if (distance > maxRadius) return;

                _hue = Math.Atan2(deltaY, deltaX) * 180 / Math.PI;
                if (_hue < 0) _hue += 360;
                
                _saturation = Math.Min(distance / maxRadius, 1.0);
                
                var newColor = HsvToRgb(_hue, _saturation, _value);
                UpdateFromColor(newColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث اللون من العجلة: {ex.Message}");
            }
        }

        private void BrightnessBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Canvas canvas)
            {
                canvas.CaptureMouse();
                UpdateBrightnessFromBar(e.GetPosition(canvas));
            }
        }

        private void BrightnessBar_MouseMove(object sender, MouseEventArgs e)
        {
            if (sender is Canvas canvas && canvas.IsMouseCaptured)
            {
                UpdateBrightnessFromBar(e.GetPosition(canvas));
            }
        }

        private void UpdateBrightnessFromBar(Point position)
        {
            try
            {
                _value = Math.Max(0, Math.Min(1, position.X / brightnessBar.ActualWidth));
                var newColor = HsvToRgb(_hue, _saturation, _value);
                UpdateFromColor(newColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث السطوع: {ex.Message}");
            }
        }

        private void AlphaBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (sender is Canvas canvas)
            {
                canvas.CaptureMouse();
                UpdateAlphaFromBar(e.GetPosition(canvas));
            }
        }

        private void AlphaBar_MouseMove(object sender, MouseEventArgs e)
        {
            if (sender is Canvas canvas && canvas.IsMouseCaptured)
            {
                UpdateAlphaFromBar(e.GetPosition(canvas));
            }
        }

        private void UpdateAlphaFromBar(Point position)
        {
            try
            {
                _alpha = Math.Max(0, Math.Min(1, position.X / alphaBar.ActualWidth));
                var newColor = HsvToRgb(_hue, _saturation, _value);
                UpdateFromColor(newColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الشفافية: {ex.Message}");
            }
        }

        private void RgbTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingFromCode) return;

            try
            {
                if (int.TryParse(txtRed.Text, out int r) && r >= 0 && r <= 255 &&
                    int.TryParse(txtGreen.Text, out int g) && g >= 0 && g <= 255 &&
                    int.TryParse(txtBlue.Text, out int b) && b >= 0 && b <= 255)
                {
                    var color = Color.FromArgb((byte)(_alpha * 255), (byte)r, (byte)g, (byte)b);
                    UpdateFromColor(color);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث RGB: {ex.Message}");
            }
        }

        private void HsvTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingFromCode) return;

            try
            {
                if (double.TryParse(txtHue.Text, out double h) && h >= 0 && h <= 360 &&
                    double.TryParse(txtSaturation.Text, out double s) && s >= 0 && s <= 100 &&
                    double.TryParse(txtValue.Text, out double v) && v >= 0 && v <= 100)
                {
                    _hue = h;
                    _saturation = s / 100.0;
                    _value = v / 100.0;
                    
                    var color = HsvToRgb(_hue, _saturation, _value);
                    UpdateFromColor(color);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث HSV: {ex.Message}");
            }
        }

        private void HexTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (_isUpdatingFromCode) return;

            try
            {
                var colorValue = txtHexColor.Text;
                if (!string.IsNullOrEmpty(colorValue))
                {
                    var color = (Color)ColorConverter.ConvertFromString(colorValue);
                    UpdateFromColor(color);
                }
            }
            catch
            {
                // تجاهل الأخطاء في تحويل اللون
            }
        }

        private void QuickColor_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Color color)
            {
                UpdateFromColor(color);
            }
        }

        private void SavedColor_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Color color)
            {
                UpdateFromColor(color);
            }
        }

        private void SavedColor_RightClick(object sender, MouseButtonEventArgs e)
        {
            if (sender is Button button && button.Tag is Color color)
            {
                _savedColors.Remove(color);
                UpdateSavedColorsPanel();
                SaveSavedColors();
            }
        }

        private void SaveColor_Click(object sender, RoutedEventArgs e)
        {
            if (!_savedColors.Contains(_selectedColor))
            {
                _savedColors.Add(_selectedColor);
                UpdateSavedColorsPanel();
                SaveSavedColors();
            }
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}