using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using InjazAcc.Services;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Suppliers
{
    /// <summary>
    /// Interaction logic for SuppliersPage.xaml
    /// </summary>
    public partial class SuppliersPage : Page
    {
        private ObservableCollection<Supplier> _suppliers = new();
        private readonly InjazAcc.DataAccess.InjazAccDbContext _dbContext;

        public SuppliersPage()
        {
            InitializeComponent();
            _dbContext = new InjazAcc.DataAccess.InjazAccDbContext();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية للموردين
                _suppliers = new ObservableCollection<Supplier>
                {
                    new Supplier { Id = 1, Code = "S001", Name = "شركة التوريدات العامة", Phone = "0555111222", Email = "<EMAIL>", Address = "الرياض - شارع العليا", OpeningBalance = 25000 },
                    new Supplier { Id = 2, Code = "S002", Name = "مؤسسة الإمداد", Phone = "0555333444", Email = "<EMAIL>", Address = "جدة - شارع فلسطين", OpeningBalance = 18000 },
                    new Supplier { Id = 3, Code = "S003", Name = "شركة المواد الأولية", Phone = "0555555666", Email = "<EMAIL>", Address = "الدمام - شارع الملك فهد", OpeningBalance = 32000 },
                    new Supplier { Id = 4, Code = "S004", Name = "مؤسسة التجهيزات", Phone = "0555777888", Email = "<EMAIL>", Address = "الرياض - شارع التخصصي", OpeningBalance = 15000 },
                    new Supplier { Id = 5, Code = "S005", Name = "شركة المعدات المكتبية", Phone = "0555999000", Email = "<EMAIL>", Address = "مكة - شارع الحج", OpeningBalance = 22000 }
                };

                // dgSuppliers.ItemsSource = _suppliers; // سيتم ربطها عند إضافة العنصر للـ XAML
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // تم إزالة UpdateStatistics لأنها تحتاج عناصر XAML غير موجودة

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تنفيذ وظيفة البحث عن الموردين", "البحث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAddSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة إضافة مورد جديد", "إضافة مورد", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة إضافة مورد جديد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة تعديل المورد المحدد", "تعديل مورد", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة تعديل المورد المحدد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteSupplier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف المورد المحدد؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("تم حذف المورد بنجاح", "حذف مورد", MessageBoxButton.OK, MessageBoxImage.Information);
                    // TODO: تنفيذ عملية الحذف
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSupplierDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح تفاصيل المورد المحدد", "تفاصيل المورد", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة تفاصيل المورد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSupplierStatement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح كشف حساب المورد المحدد", "كشف حساب المورد", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح كشف حساب المورد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSupplierPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة تسجيل دفعة للمورد", "دفعة لمورد", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة تسجيل دفعة للمورد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnRefresh_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadSampleData();
                MessageBox.Show("تم تحديث قائمة الموردين", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportSuppliers_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تصدير قائمة الموردين إلى ملف Excel", "تصدير الموردين", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تنفيذ تصدير الموردين
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPrintSuppliers_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم طباعة قائمة الموردين", "طباعة الموردين", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: تنفيذ طباعة الموردين
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSupplierReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح تقرير الموردين", "تقرير الموردين", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح تقرير الموردين
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnExportToExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تصدير قائمة الموردين إلى Excel", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgSuppliers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (dgSuppliers.SelectedItem != null)
                {
                    MessageBox.Show("سيتم فتح تفاصيل المورد", "تفاصيل المورد", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
