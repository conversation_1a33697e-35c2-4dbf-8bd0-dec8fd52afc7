using System;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Dialogs
{
    public partial class AddProductDialog : Window
    {
        public Product Product { get; private set; }
        public bool IsSuccess { get; private set; }

        public AddProductDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        public AddProductDialog(Product product) : this()
        {
            if (product != null)
            {
                LoadProductData(product);
                Title = "تعديل بيانات المنتج";
            }
        }

        private void InitializeDialog()
        {
            // تعيين القيم الافتراضية
            cmbUnit.SelectedIndex = 0; // قطعة
            cmbCategory.SelectedIndex = 5; // أخرى
            
            // تركيز على حقل الاسم
            txtProductName.Focus();
        }

        private void LoadProductData(Product product)
        {
            txtProductCode.Text = product.Code;
            txtProductName.Text = product.Name;
            txtDescription.Text = product.Description;
            
            // تعيين الفئة
            if (product.Category != null)
            {
                foreach (ComboBoxItem item in cmbCategory.Items)
                {
                    if (item.Content.ToString() == product.Category.Name)
                    {
                        cmbCategory.SelectedItem = item;
                        break;
                    }
                }
            }

            // تعيين الوحدة
            if (product.Unit != null)
            {
                foreach (ComboBoxItem item in cmbUnit.Items)
                {
                    if (item.Content.ToString() == product.Unit.Name)
                    {
                        cmbUnit.SelectedItem = item;
                        break;
                    }
                }
            }

            txtPurchasePrice.Text = product.PurchasePrice.ToString();
            txtSalePrice.Text = product.SalePrice.ToString();
            txtMinimumStock.Text = product.MinimumStock.ToString();
            chkIsActive.IsChecked = product.IsActive;
        }

        private bool ValidateInput()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtProductName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المنتج", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtProductName.Focus();
                return false;
            }

            if (cmbUnit.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار وحدة المنتج", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                cmbUnit.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtSalePrice.Text))
            {
                MessageBox.Show("يرجى إدخال سعر البيع", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtSalePrice.Focus();
                return false;
            }

            // التحقق من الأرقام
            if (!decimal.TryParse(txtSalePrice.Text, out decimal salePrice) || salePrice <= 0)
            {
                MessageBox.Show("سعر البيع يجب أن يكون رقماً موجباً", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtSalePrice.Focus();
                return false;
            }

            if (!string.IsNullOrWhiteSpace(txtPurchasePrice.Text))
            {
                if (!decimal.TryParse(txtPurchasePrice.Text, out decimal purchasePrice) || purchasePrice < 0)
                {
                    MessageBox.Show("سعر الشراء يجب أن يكون رقماً غير سالب", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPurchasePrice.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(txtOpeningQuantity.Text))
            {
                if (!decimal.TryParse(txtOpeningQuantity.Text, out decimal openingQuantity) || openingQuantity < 0)
                {
                    MessageBox.Show("الكمية الافتتاحية يجب أن تكون رقماً غير سالب", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtOpeningQuantity.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(txtMinimumStock.Text))
            {
                if (!decimal.TryParse(txtMinimumStock.Text, out decimal minStock) || minStock < 0)
                {
                    MessageBox.Show("الحد الأدنى للمخزون يجب أن يكون رقماً غير سالب", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtMinimumStock.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(txtMaximumStock.Text))
            {
                if (!decimal.TryParse(txtMaximumStock.Text, out decimal maxStock) || maxStock < 0)
                {
                    MessageBox.Show("الحد الأقصى للمخزون يجب أن يكون رقماً غير سالب", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtMaximumStock.Focus();
                    return false;
                }

                // التحقق من أن الحد الأقصى أكبر من الحد الأدنى
                if (!string.IsNullOrWhiteSpace(txtMinimumStock.Text))
                {
                    if (decimal.TryParse(txtMinimumStock.Text, out decimal minStock) && maxStock < minStock)
                    {
                        MessageBox.Show("الحد الأقصى للمخزون يجب أن يكون أكبر من الحد الأدنى", "خطأ في البيانات", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        txtMaximumStock.Focus();
                        return false;
                    }
                }
            }

            return true;
        }

        private Product CreateProductFromInput()
        {
            var product = new Product
            {
                Code = txtProductCode.Text?.Trim(),
                Name = txtProductName.Text?.Trim(),
                Description = txtDescription.Text?.Trim(),
                IsActive = chkIsActive.IsChecked ?? true,
                CreatedAt = DateTime.Now
            };

            // تحويل الأرقام
            if (decimal.TryParse(txtPurchasePrice.Text, out decimal purchasePrice))
                product.PurchasePrice = purchasePrice;

            if (decimal.TryParse(txtSalePrice.Text, out decimal salePrice))
                product.SalePrice = salePrice;

            if (decimal.TryParse(txtMinimumStock.Text, out decimal minimumStock))
                product.MinimumStock = minimumStock;

            // تعيين معرفات الفئة والوحدة (يجب أن تكون من قاعدة البيانات في التطبيق الحقيقي)
            product.CategoryId = cmbCategory.SelectedIndex + 1; // مؤقت
            product.UnitId = cmbUnit.SelectedIndex + 1; // مؤقت

            return product;
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                Product = CreateProductFromInput();
                IsSuccess = true;
                
                MessageBox.Show("تم حفظ بيانات المنتج بنجاح", "نجح الحفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            IsSuccess = false;
            DialogResult = false;
            Close();
        }
    }
}
