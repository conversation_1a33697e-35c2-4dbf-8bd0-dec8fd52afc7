using System;
using System.Windows;
using System.Windows.Controls;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Dialogs
{
    public partial class AddSupplierDialog : Window
    {
        public Supplier Supplier { get; private set; }
        public bool IsSuccess { get; private set; }

        public AddSupplierDialog()
        {
            InitializeComponent();
            InitializeDialog();
        }

        public AddSupplierDialog(Supplier supplier) : this()
        {
            if (supplier != null)
            {
                LoadSupplierData(supplier);
                Title = "تعديل بيانات المورد";
            }
        }

        private void InitializeDialog()
        {
            // تعيين التاريخ الافتراضي
            dpOpeningBalanceDate.SelectedDate = DateTime.Now;
            
            // تركيز على حقل الاسم
            txtSupplierName.Focus();
        }

        private void LoadSupplierData(Supplier supplier)
        {
            txtSupplierCode.Text = supplier.Code;
            txtSupplierName.Text = supplier.Name;
            txtContactPerson.Text = supplier.ContactPerson;
            txtPhone.Text = supplier.Phone;
            txtEmail.Text = supplier.Email;
            txtAddress.Text = supplier.Address;
            txtTaxNumber.Text = supplier.TaxNumber;
            txtOpeningBalance.Text = supplier.OpeningBalance.ToString();
            dpOpeningBalanceDate.SelectedDate = supplier.OpeningBalanceDate;
            txtNotes.Text = supplier.Notes;
            chkIsActive.IsChecked = supplier.IsActive;
        }

        private bool ValidateInput()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtSupplierName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "خطأ في البيانات", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtSupplierName.Focus();
                return false;
            }

            // التحقق من صحة البريد الإلكتروني
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                try
                {
                    var addr = new System.Net.Mail.MailAddress(txtEmail.Text);
                    if (addr.Address != txtEmail.Text)
                    {
                        MessageBox.Show("البريد الإلكتروني غير صحيح", "خطأ في البيانات", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        txtEmail.Focus();
                        return false;
                    }
                }
                catch
                {
                    MessageBox.Show("البريد الإلكتروني غير صحيح", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtEmail.Focus();
                    return false;
                }
            }

            // التحقق من الأرقام
            if (!string.IsNullOrWhiteSpace(txtCreditLimit.Text))
            {
                if (!decimal.TryParse(txtCreditLimit.Text, out _))
                {
                    MessageBox.Show("حد الائتمان يجب أن يكون رقماً صحيحاً", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCreditLimit.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(txtOpeningBalance.Text))
            {
                if (!decimal.TryParse(txtOpeningBalance.Text, out _))
                {
                    MessageBox.Show("الرصيد الافتتاحي يجب أن يكون رقماً صحيحاً", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtOpeningBalance.Focus();
                    return false;
                }
            }

            return true;
        }

        private Supplier CreateSupplierFromInput()
        {
            var supplier = new Supplier
            {
                Code = txtSupplierCode.Text?.Trim(),
                Name = txtSupplierName.Text?.Trim(),
                ContactPerson = txtContactPerson.Text?.Trim(),
                Phone = txtPhone.Text?.Trim(),
                Email = txtEmail.Text?.Trim(),
                Address = txtAddress.Text?.Trim(),
                TaxNumber = txtTaxNumber.Text?.Trim(),
                Notes = txtNotes.Text?.Trim(),
                IsActive = chkIsActive.IsChecked ?? true,
                OpeningBalanceDate = dpOpeningBalanceDate.SelectedDate ?? DateTime.Now,
                CreatedAt = DateTime.Now
            };

            if (decimal.TryParse(txtOpeningBalance.Text, out decimal openingBalance))
                supplier.OpeningBalance = openingBalance;

            return supplier;
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                Supplier = CreateSupplierFromInput();
                IsSuccess = true;
                
                MessageBox.Show("تم حفظ بيانات المورد بنجاح", "نجح الحفظ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            IsSuccess = false;
            DialogResult = false;
            Close();
        }
    }
}
