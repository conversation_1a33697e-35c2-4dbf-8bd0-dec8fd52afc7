import os
import sqlite3
import json
from datetime import datetime

def diagnose_settings():
    """تشخيص حالة الإعدادات"""
    print("🔍 جاري تشخيص حالة الإعدادات...")
    print("=" * 60)
    
    # مسارات الإعدادات
    app_data = os.path.expanduser("~\\AppData\\Roaming\\InjazAcc")
    settings_db = os.path.join(app_data, "settings.db")
    theme_settings = os.path.join(app_data, "theme_settings.json")
    advanced_color_settings = os.path.join(app_data, "advanced_color_settings.json")
    backup_json = os.path.join(app_data, "settings_backup.json")
    
    print(f"📁 مجلد الإعدادات: {app_data}")
    print(f"   موجود: {'✅' if os.path.exists(app_data) else '❌'}")
    
    # فحص قاعدة بيانات الإعدادات
    print(f"\n💾 قاعدة بيانات الإعدادات: {settings_db}")
    if os.path.exists(settings_db):
        print("   موجودة: ✅")
        try:
            conn = sqlite3.connect(settings_db)
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"   الجداول: {[table[0] for table in tables]}")
            
            # عدد الإعدادات
            cursor.execute("SELECT COUNT(*) FROM SystemSettings;")
            count = cursor.fetchone()[0]
            print(f"   عدد الإعدادات: {count}")
            
            # آخر تحديث
            cursor.execute("SELECT MAX(UpdatedAt) FROM SystemSettings;")
            last_update = cursor.fetchone()[0]
            print(f"   آخر تحديث: {last_update}")
            
            # عرض بعض الإعدادات
            cursor.execute("SELECT SettingKey, SettingValue FROM SystemSettings LIMIT 5;")
            settings = cursor.fetchall()
            print("   عينة من الإعدادات:")
            for key, value in settings:
                print(f"     {key}: {value}")
            
            conn.close()
        except Exception as e:
            print(f"   خطأ في قراءة قاعدة البيانات: {e}")
    else:
        print("   موجودة: ❌")
    
    # فحص إعدادات الألوان
    print(f"\n🎨 إعدادات الألوان: {theme_settings}")
    if os.path.exists(theme_settings):
        print("   موجودة: ✅")
        try:
            with open(theme_settings, 'r', encoding='utf-8') as f:
                theme_data = json.load(f)
            print(f"   اللون الأساسي: {theme_data.get('PrimaryColor', 'غير محدد')}")
            print(f"   المظهر: {theme_data.get('BaseTheme', 'غير محدد')}")
        except Exception as e:
            print(f"   خطأ في قراءة الملف: {e}")
    else:
        print("   موجودة: ❌")
    
    # فحص إعدادات الألوان المتقدمة
    print(f"\n🌈 إعدادات الألوان المتقدمة: {advanced_color_settings}")
    if os.path.exists(advanced_color_settings):
        print("   موجودة: ✅")
        try:
            with open(advanced_color_settings, 'r', encoding='utf-8') as f:
                advanced_data = json.load(f)
            print(f"   عدد الألوان المخصصة: {len(advanced_data.get('CustomColors', {}))}")
        except Exception as e:
            print(f"   خطأ في قراءة الملف: {e}")
    else:
        print("   موجودة: ❌")
    
    # فحص النسخة الاحتياطية
    print(f"\n💾 النسخة الاحتياطية JSON: {backup_json}")
    if os.path.exists(backup_json):
        print("   موجودة: ✅")
        try:
            with open(backup_json, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            print(f"   عدد الإعدادات في النسخة الاحتياطية: {len(backup_data)}")
        except Exception as e:
            print(f"   خطأ في قراءة الملف: {e}")
    else:
        print("   موجودة: ❌")
    
    print("\n" + "=" * 60)
    print("✅ انتهى التشخيص")

def create_test_settings():
    """إنشاء إعدادات تجريبية للاختبار"""
    print("🧪 جاري إنشاء إعدادات تجريبية...")
    
    app_data = os.path.expanduser("~\\AppData\\Roaming\\InjazAcc")
    if not os.path.exists(app_data):
        os.makedirs(app_data)
        print(f"✅ تم إنشاء مجلد: {app_data}")
    
    # إنشاء قاعدة بيانات تجريبية
    settings_db = os.path.join(app_data, "settings.db")
    conn = sqlite3.connect(settings_db)
    cursor = conn.cursor()
    
    # إنشاء الجدول
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS SystemSettings (
            Id INTEGER PRIMARY KEY,
            SettingKey TEXT NOT NULL UNIQUE,
            SettingValue TEXT,
            Description TEXT,
            [Group] INTEGER,
            IsReadOnly INTEGER DEFAULT 0,
            CreatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
            UpdatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إضافة إعدادات تجريبية
    test_settings = [
        (1, "TestSetting1", "Value1", "إعداد تجريبي 1", 0, 0),
        (2, "TestSetting2", "Value2", "إعداد تجريبي 2", 0, 0),
        (3, "LastAppCloseTime", datetime.now().isoformat(), "آخر إغلاق للتطبيق", 1, 0),
        (4, "AppClosedProperly", "true", "إغلاق صحيح للتطبيق", 1, 0),
    ]
    
    for setting in test_settings:
        cursor.execute('''
            INSERT OR REPLACE INTO SystemSettings 
            (Id, SettingKey, SettingValue, Description, [Group], IsReadOnly)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', setting)
    
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء قاعدة بيانات الإعدادات التجريبية")
    
    # إنشاء ملف إعدادات الألوان
    theme_settings = {
        "PrimaryColor": "Teal",
        "SecondaryColor": "Amber", 
        "BaseTheme": "Light",
        "BackgroundColor": "#FFFFFF",
        "TextColor": "#000000"
    }
    
    theme_file = os.path.join(app_data, "theme_settings.json")
    with open(theme_file, 'w', encoding='utf-8') as f:
        json.dump(theme_settings, f, indent=2, ensure_ascii=False)
    
    print("✅ تم إنشاء ملف إعدادات الألوان")
    print("🎉 تم إنشاء جميع الإعدادات التجريبية بنجاح!")

if __name__ == "__main__":
    print("🔧 أداة تشخيص الإعدادات")
    print("=" * 60)
    print("1. تشخيص الإعدادات الحالية")
    print("2. إنشاء إعدادات تجريبية")
    print("=" * 60)
    
    choice = input("اختر الخيار (1 أو 2): ").strip()
    
    if choice == "1":
        diagnose_settings()
    elif choice == "2":
        create_test_settings()
        print("\nتشخيص الإعدادات بعد الإنشاء:")
        diagnose_settings()
    else:
        print("❌ خيار غير صحيح")
    
    input("\nاضغط Enter للخروج...")