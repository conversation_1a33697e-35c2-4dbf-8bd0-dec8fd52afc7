using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Windows;
using System.Windows.Media;
using MaterialDesignThemes.Wpf;

namespace InjazAcc.UI.Helpers
{
    /// <summary>
    /// مدير الألوان والمظاهر للتطبيق
    /// </summary>
    public static class ThemeManager
    {
        private static readonly string SettingsPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "InjazAcc", "theme_settings.json");

        /// <summary>
        /// إعدادات الألوان
        /// </summary>
        public class ColorSettings
        {
            public string PrimaryColor { get; set; } = "Teal";
            public string SecondaryColor { get; set; } = "Amber";
            public string BaseTheme { get; set; } = "Light";
            public string BackgroundColor { get; set; } = "#FFFFFF";
            public string TextColor { get; set; } = "#000000";
            public string BorderColor { get; set; } = "#CCCCCC";
            public string ButtonColor { get; set; } = "#009688";
            public string SelectionColor { get; set; } = "#E3F2FD";
            public string WarningColor { get; set; } = "#FF9800";
        }

        private static ColorSettings _currentSettings = new ColorSettings();

        /// <summary>
        /// الحصول على الإعدادات الحالية
        /// </summary>
        public static ColorSettings CurrentSettings => _currentSettings;

        /// <summary>
        /// تحميل إعدادات الألوان
        /// </summary>
        public static void LoadSettings()
        {
            try
            {
                if (File.Exists(SettingsPath))
                {
                    var json = File.ReadAllText(SettingsPath);
                    _currentSettings = JsonSerializer.Deserialize<ColorSettings>(json) ?? new ColorSettings();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل إعدادات الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// حفظ إعدادات الألوان
        /// </summary>
        public static void SaveSettings()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    Directory.CreateDirectory(directory);

                var json = JsonSerializer.Serialize(_currentSettings, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                File.WriteAllText(SettingsPath, json);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ إعدادات الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// تطبيق الألوان على التطبيق
        /// </summary>
        public static void ApplyTheme()
        {
            try
            {
                var app = Application.Current;
                if (app?.Resources == null) return;

                // تحديث المظهر الأساسي
                var paletteHelper = new PaletteHelper();
                var theme = paletteHelper.GetTheme();

                // تعيين المظهر الأساسي (فاتح/داكن)
                if (_currentSettings.BaseTheme == "Dark")
                    theme.SetBaseTheme(Theme.Dark);
                else
                    theme.SetBaseTheme(Theme.Light);

                // تعيين الألوان الأساسية
                var primaryColor = GetMaterialDesignColor(_currentSettings.PrimaryColor);
                var secondaryColor = GetMaterialDesignColor(_currentSettings.SecondaryColor);

                theme.SetPrimaryColor(primaryColor);
                theme.SetSecondaryColor(secondaryColor);

                paletteHelper.SetTheme(theme);

                // تطبيق الألوان المخصصة
                ApplyCustomColors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// تطبيق الألوان المخصصة
        /// </summary>
        private static void ApplyCustomColors()
        {
            var app = Application.Current;
            if (app?.Resources == null) return;

            try
            {
                // تحديث الألوان المخصصة في الموارد
                app.Resources["CustomBackgroundColor"] = new SolidColorBrush(
                    (Color)ColorConverter.ConvertFromString(_currentSettings.BackgroundColor));
                
                app.Resources["CustomTextColor"] = new SolidColorBrush(
                    (Color)ColorConverter.ConvertFromString(_currentSettings.TextColor));
                
                app.Resources["CustomBorderColor"] = new SolidColorBrush(
                    (Color)ColorConverter.ConvertFromString(_currentSettings.BorderColor));
                
                app.Resources["CustomButtonColor"] = new SolidColorBrush(
                    (Color)ColorConverter.ConvertFromString(_currentSettings.ButtonColor));
                
                app.Resources["CustomSelectionColor"] = new SolidColorBrush(
                    (Color)ColorConverter.ConvertFromString(_currentSettings.SelectionColor));
                
                app.Resources["CustomWarningColor"] = new SolidColorBrush(
                    (Color)ColorConverter.ConvertFromString(_currentSettings.WarningColor));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الألوان المخصصة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// الحصول على لون Material Design
        /// </summary>
        private static Color GetMaterialDesignColor(string colorName)
        {
            return colorName.ToLower() switch
            {
                "teal" => Colors.Teal,
                "blue" => Colors.Blue,
                "green" => Colors.Green,
                "orange" => Colors.Orange,
                "red" => Colors.Red,
                "purple" => Colors.Purple,
                "pink" => Colors.Pink,
                "brown" => Colors.Brown,
                "grey" => Colors.Gray,
                "indigo" => Colors.Indigo,
                "amber" => Colors.Gold,
                "yellow" => Colors.Yellow,
                "lightgreen" => Colors.LightGreen,
                "lightblue" => Colors.LightBlue,
                _ => Colors.Teal
            };
        }

        /// <summary>
        /// تطبيق قالب جاهز
        /// </summary>
        public static void ApplyPresetTheme(string themeName)
        {
            switch (themeName.ToLower())
            {
                case "classic":
                    _currentSettings.PrimaryColor = "Indigo";
                    _currentSettings.SecondaryColor = "Amber";
                    _currentSettings.BaseTheme = "Light";
                    _currentSettings.ButtonColor = "#3F51B5";
                    break;

                case "modern":
                    _currentSettings.PrimaryColor = "Teal";
                    _currentSettings.SecondaryColor = "Orange";
                    _currentSettings.BaseTheme = "Light";
                    _currentSettings.ButtonColor = "#009688";
                    break;

                case "warm":
                    _currentSettings.PrimaryColor = "Orange";
                    _currentSettings.SecondaryColor = "Red";
                    _currentSettings.BaseTheme = "Light";
                    _currentSettings.ButtonColor = "#FF9800";
                    break;

                case "cool":
                    _currentSettings.PrimaryColor = "Blue";
                    _currentSettings.SecondaryColor = "LightBlue";
                    _currentSettings.BaseTheme = "Light";
                    _currentSettings.ButtonColor = "#2196F3";
                    break;

                case "nature":
                    _currentSettings.PrimaryColor = "Green";
                    _currentSettings.SecondaryColor = "LightGreen";
                    _currentSettings.BaseTheme = "Light";
                    _currentSettings.ButtonColor = "#4CAF50";
                    break;

                case "elegant":
                    _currentSettings.PrimaryColor = "Purple";
                    _currentSettings.SecondaryColor = "Pink";
                    _currentSettings.BaseTheme = "Dark";
                    _currentSettings.ButtonColor = "#9C27B0";
                    break;
            }

            ApplyTheme();
        }

        /// <summary>
        /// إعادة تعيين الألوان للافتراضية
        /// </summary>
        public static void ResetToDefault()
        {
            _currentSettings = new ColorSettings();
            ApplyTheme();
        }

        /// <summary>
        /// تحديث لون مخصص
        /// </summary>
        public static void UpdateCustomColor(string colorType, string colorValue)
        {
            switch (colorType.ToLower())
            {
                case "background":
                    _currentSettings.BackgroundColor = colorValue;
                    break;
                case "text":
                    _currentSettings.TextColor = colorValue;
                    break;
                case "border":
                    _currentSettings.BorderColor = colorValue;
                    break;
                case "button":
                    _currentSettings.ButtonColor = colorValue;
                    break;
                case "selection":
                    _currentSettings.SelectionColor = colorValue;
                    break;
                case "warning":
                    _currentSettings.WarningColor = colorValue;
                    break;
            }
        }

        /// <summary>
        /// تحديث الألوان الأساسية
        /// </summary>
        public static void UpdatePrimaryColors(string primary, string secondary, string baseTheme)
        {
            _currentSettings.PrimaryColor = primary;
            _currentSettings.SecondaryColor = secondary;
            _currentSettings.BaseTheme = baseTheme;
        }
    }
}