<Window x:Class="InjazAcc.UI.Views.Dialogs.AddInvoiceDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة فاتورة جديدة" Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إضافة فاتورة جديدة" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- معلومات الفاتورة الأساسية -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- رقم الفاتورة -->
            <TextBox x:Name="txtInvoiceNumber" Grid.Row="0" Grid.Column="0"
                     materialDesign:HintAssist.Hint="رقم الفاتورة *"
                     Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                     Margin="0,5,10,5"/>

            <!-- تاريخ الفاتورة -->
            <DatePicker x:Name="dpInvoiceDate" Grid.Row="0" Grid.Column="1"
                        materialDesign:HintAssist.Hint="تاريخ الفاتورة *"
                        Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                        Margin="5,5"/>

            <!-- نوع الفاتورة -->
            <ComboBox x:Name="cmbInvoiceType" Grid.Row="0" Grid.Column="2"
                      materialDesign:HintAssist.Hint="نوع الفاتورة *"
                      Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                      Margin="10,5,0,5">
                <ComboBoxItem Content="فاتورة بيع"/>
                <ComboBoxItem Content="فاتورة شراء"/>
                <ComboBoxItem Content="فاتورة مرتجع بيع"/>
                <ComboBoxItem Content="فاتورة مرتجع شراء"/>
            </ComboBox>

            <!-- العميل/المورد -->
            <ComboBox x:Name="cmbCustomer" Grid.Row="1" Grid.Column="0"
                      materialDesign:HintAssist.Hint="العميل/المورد *"
                      Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                      Margin="0,5,10,5" IsEditable="True"/>

            <!-- طريقة الدفع -->
            <ComboBox x:Name="cmbPaymentMethod" Grid.Row="1" Grid.Column="1"
                      materialDesign:HintAssist.Hint="طريقة الدفع"
                      Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                      Margin="5,5">
                <ComboBoxItem Content="نقداً"/>
                <ComboBoxItem Content="آجل"/>
                <ComboBoxItem Content="شيك"/>
                <ComboBoxItem Content="تحويل بنكي"/>
                <ComboBoxItem Content="بطاقة ائتمان"/>
            </ComboBox>

            <!-- تاريخ الاستحقاق -->
            <DatePicker x:Name="dpDueDate" Grid.Row="1" Grid.Column="2"
                        materialDesign:HintAssist.Hint="تاريخ الاستحقاق"
                        Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                        Margin="10,5,0,5"/>

            <!-- ملاحظات -->
            <TextBox x:Name="txtNotes" Grid.Row="2" Grid.ColumnSpan="3"
                     materialDesign:HintAssist.Hint="ملاحظات"
                     Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                     AcceptsReturn="True" TextWrapping="Wrap"
                     MinLines="2" MaxLines="3"
                     Margin="0,5"/>
        </Grid>

        <!-- أصناف الفاتورة -->
        <GroupBox Grid.Row="2" Header="أصناف الفاتورة" 
                  Style="{StaticResource MaterialDesignCardGroupBox}"
                  Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- أزرار إدارة الأصناف -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <Button x:Name="btnAddItem" Content="إضافة صنف"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Background="{DynamicResource PrimaryHueMidBrush}"
                            Foreground="White"
                            Margin="0,0,10,0"
                            Click="btnAddItem_Click"/>
                    
                    <Button x:Name="btnRemoveItem" Content="حذف صنف"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Margin="0,0,10,0"
                            Click="btnRemoveItem_Click"/>
                </StackPanel>

                <!-- جدول الأصناف -->
                <DataGrid x:Name="dgItems" Grid.Row="1"
                          Style="{StaticResource MaterialDesignDataGrid}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          SelectionMode="Single">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="كود الصنف" Binding="{Binding ProductCode}" Width="100"/>
                        <DataGridTextColumn Header="اسم الصنف" Binding="{Binding ProductName}" Width="*"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                        <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice}" Width="100"/>
                        <DataGridTextColumn Header="الخصم" Binding="{Binding Discount}" Width="80"/>
                        <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total}" Width="100" IsReadOnly="True"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </GroupBox>

        <!-- ملخص الفاتورة -->
        <Grid Grid.Row="3" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="المجموع الفرعي:" HorizontalAlignment="Right" Margin="0,0,10,0"/>
                    <TextBox x:Name="txtSubTotal" Grid.Column="1" IsReadOnly="True" Text="0.00"/>
                </Grid>

                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="الخصم الإجمالي:" HorizontalAlignment="Right" Margin="0,0,10,0"/>
                    <TextBox x:Name="txtTotalDiscount" Grid.Column="1" Text="0.00"/>
                </Grid>

                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="الضريبة:" HorizontalAlignment="Right" Margin="0,0,10,0"/>
                    <TextBox x:Name="txtTax" Grid.Column="1" Text="0.00"/>
                </Grid>

                <Grid Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="100"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="الإجمالي النهائي:" HorizontalAlignment="Right" Margin="0,0,10,0" FontWeight="Bold"/>
                    <TextBox x:Name="txtGrandTotal" Grid.Column="1" IsReadOnly="True" Text="0.00" FontWeight="Bold"/>
                </Grid>
            </StackPanel>
        </Grid>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="btnSave" Content="حفظ" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Foreground="White"
                    Width="100" Margin="10,0"
                    Click="btnSave_Click"/>
            
            <Button x:Name="btnPrint" Content="طباعة" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="100" Margin="10,0"
                    Click="btnPrint_Click"/>
            
            <Button x:Name="btnCancel" Content="إلغاء" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="100" Margin="10,0"
                    Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
