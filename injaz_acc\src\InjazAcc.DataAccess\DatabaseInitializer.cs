using InjazAcc.Core.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace InjazAcc.DataAccess
{
    /// <summary>
    /// خدمة تهيئة قاعدة البيانات والبيانات الأولية
    /// </summary>
    public static class DatabaseInitializer
    {
        /// <summary>
        /// تهيئة قاعدة البيانات مع البيانات الأولية
        /// </summary>
        public static async Task InitializeAsync(InjazAccDbContext context)
        {
            try
            {
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                await context.Database.EnsureCreatedAsync();

                // تطبيق أي migrations معلقة
                if (context.Database.GetPendingMigrations().Any())
                {
                    await context.Database.MigrateAsync();
                }

                // إضافة البيانات الأولية إذا لم تكن موجودة
                await SeedDataAsync(context);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إضافة البيانات الأولية
        /// </summary>
        private static async Task SeedDataAsync(InjazAccDbContext context)
        {
            // إضافة الأدوار الأساسية
            await SeedRolesAsync(context);

            // إضافة الصلاحيات الأساسية
            await SeedPermissionsAsync(context);

            // إضافة المستخدم الافتراضي
            await SeedUsersAsync(context);

            // إضافة الوحدات الأساسية
            await SeedUnitsAsync(context);

            // إضافة الفئات الأساسية
            await SeedCategoriesAsync(context);

            // إضافة المخازن الأساسية
            await SeedWarehousesAsync(context);

            // إضافة الحسابات الأساسية
            await SeedAccountsAsync(context);

            // إضافة معلومات الشركة الافتراضية
            await SeedCompanyInfoAsync(context);

            // إضافة السنة المالية الافتراضية
            await SeedFiscalYearAsync(context);

            // إضافة إعدادات النظام الافتراضية
            await SeedSystemSettingsAsync(context);

            // حفظ التغييرات
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// إضافة الأدوار الأساسية
        /// </summary>
        private static async Task SeedRolesAsync(InjazAccDbContext context)
        {
            if (!await context.Roles.AnyAsync())
            {
                var roles = new List<Role>
                {
                    new Role { Name = "مدير النظام", Description = "صلاحيات كاملة على النظام" },
                    new Role { Name = "مدير مالي", Description = "إدارة العمليات المالية والمحاسبية" },
                    new Role { Name = "مدير مبيعات", Description = "إدارة عمليات المبيعات والعملاء" },
                    new Role { Name = "مدير مشتريات", Description = "إدارة عمليات المشتريات والموردين" },
                    new Role { Name = "مدير مخزون", Description = "إدارة المخزون والمنتجات" },
                    new Role { Name = "محاسب", Description = "العمليات المحاسبية الأساسية" },
                    new Role { Name = "كاشير", Description = "عمليات البيع والتحصيل" },
                    new Role { Name = "مستخدم", Description = "صلاحيات محدودة للاستعلام" }
                };

                await context.Roles.AddRangeAsync(roles);
            }
        }

        /// <summary>
        /// إضافة الصلاحيات الأساسية
        /// </summary>
        private static async Task SeedPermissionsAsync(InjazAccDbContext context)
        {
            if (!await context.Permissions.AnyAsync())
            {
                var permissions = new List<Permission>
                {
                    // صلاحيات المبيعات
                    new Permission { Name = "عرض المبيعات", Code = "SALES_VIEW", Description = "عرض فواتير المبيعات" },
                    new Permission { Name = "إضافة مبيعات", Code = "SALES_CREATE", Description = "إنشاء فواتير مبيعات جديدة" },
                    new Permission { Name = "تعديل المبيعات", Code = "SALES_EDIT", Description = "تعديل فواتير المبيعات" },
                    new Permission { Name = "حذف المبيعات", Code = "SALES_DELETE", Description = "حذف فواتير المبيعات" },

                    // صلاحيات المشتريات
                    new Permission { Name = "عرض المشتريات", Code = "PURCHASES_VIEW", Description = "عرض فواتير المشتريات" },
                    new Permission { Name = "إضافة مشتريات", Code = "PURCHASES_CREATE", Description = "إنشاء فواتير مشتريات جديدة" },
                    new Permission { Name = "تعديل المشتريات", Code = "PURCHASES_EDIT", Description = "تعديل فواتير المشتريات" },
                    new Permission { Name = "حذف المشتريات", Code = "PURCHASES_DELETE", Description = "حذف فواتير المشتريات" },

                    // صلاحيات المخزون
                    new Permission { Name = "عرض المخزون", Code = "INVENTORY_VIEW", Description = "عرض المنتجات والمخزون" },
                    new Permission { Name = "إدارة المنتجات", Code = "PRODUCTS_MANAGE", Description = "إضافة وتعديل المنتجات" },
                    new Permission { Name = "نقل المخزون", Code = "INVENTORY_TRANSFER", Description = "نقل المخزون بين المخازن" },

                    // صلاحيات العملاء والموردين
                    new Permission { Name = "إدارة العملاء", Code = "CUSTOMERS_MANAGE", Description = "إدارة بيانات العملاء" },
                    new Permission { Name = "إدارة الموردين", Code = "SUPPLIERS_MANAGE", Description = "إدارة بيانات الموردين" },

                    // صلاحيات المحاسبة
                    new Permission { Name = "عرض التقارير المالية", Code = "REPORTS_VIEW", Description = "عرض التقارير المالية" },
                    new Permission { Name = "إدارة الحسابات", Code = "ACCOUNTS_MANAGE", Description = "إدارة دليل الحسابات" },
                    new Permission { Name = "القيود المحاسبية", Code = "JOURNAL_ENTRIES", Description = "إنشاء وتعديل القيود المحاسبية" },

                    // صلاحيات النظام
                    new Permission { Name = "إدارة المستخدمين", Code = "USERS_MANAGE", Description = "إدارة المستخدمين والصلاحيات" },
                    new Permission { Name = "إعدادات النظام", Code = "SYSTEM_SETTINGS", Description = "تعديل إعدادات النظام" },
                    new Permission { Name = "النسخ الاحتياطي", Code = "BACKUP_RESTORE", Description = "إنشاء واستعادة النسخ الاحتياطية" }
                };

                await context.Permissions.AddRangeAsync(permissions);
            }
        }

        /// <summary>
        /// إضافة المستخدم الافتراضي
        /// </summary>
        private static async Task SeedUsersAsync(InjazAccDbContext context)
        {
            if (!await context.Users.AnyAsync())
            {
                var adminRole = await context.Roles.FirstOrDefaultAsync(r => r.Name == "مدير النظام");
                if (adminRole != null)
                {
                    var adminUser = new User
                    {
                        Username = "admin",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"), // كلمة مرور افتراضية
                        FullName = "مدير النظام",
                        Email = "<EMAIL>",
                        IsActive = true,
                        CreatedAt = DateTime.Now,
                        RoleId = adminRole.Id
                    };

                    await context.Users.AddAsync(adminUser);
                }
            }
        }

        /// <summary>
        /// إضافة الوحدات الأساسية
        /// </summary>
        private static async Task SeedUnitsAsync(InjazAccDbContext context)
        {
            if (!await context.Units.AnyAsync())
            {
                var units = new List<Unit>
                {
                    new Unit { Name = "قطعة", Symbol = "قطعة", Description = "وحدة العد الأساسية" },
                    new Unit { Name = "كيلوجرام", Symbol = "كجم", Description = "وحدة الوزن" },
                    new Unit { Name = "جرام", Symbol = "جم", Description = "وحدة الوزن الصغيرة" },
                    new Unit { Name = "لتر", Symbol = "لتر", Description = "وحدة الحجم" },
                    new Unit { Name = "متر", Symbol = "م", Description = "وحدة الطول" },
                    new Unit { Name = "متر مربع", Symbol = "م²", Description = "وحدة المساحة" },
                    new Unit { Name = "متر مكعب", Symbol = "م³", Description = "وحدة الحجم" },
                    new Unit { Name = "صندوق", Symbol = "صندوق", Description = "وحدة التعبئة" },
                    new Unit { Name = "كرتون", Symbol = "كرتون", Description = "وحدة التعبئة الكبيرة" },
                    new Unit { Name = "طن", Symbol = "طن", Description = "وحدة الوزن الكبيرة" }
                };

                await context.Units.AddRangeAsync(units);
            }
        }

        /// <summary>
        /// إضافة الفئات الأساسية
        /// </summary>
        private static async Task SeedCategoriesAsync(InjazAccDbContext context)
        {
            if (!await context.Categories.AnyAsync())
            {
                var categories = new List<Category>
                {
                    new Category { Name = "مواد غذائية", Description = "المنتجات الغذائية والمشروبات" },
                    new Category { Name = "مواد تنظيف", Description = "منتجات التنظيف والنظافة" },
                    new Category { Name = "أدوات مكتبية", Description = "القرطاسية والأدوات المكتبية" },
                    new Category { Name = "إلكترونيات", Description = "الأجهزة الإلكترونية والكهربائية" },
                    new Category { Name = "ملابس", Description = "الملابس والأزياء" },
                    new Category { Name = "أدوات منزلية", Description = "الأدوات والأجهزة المنزلية" },
                    new Category { Name = "مواد بناء", Description = "مواد ومعدات البناء" },
                    new Category { Name = "قطع غيار", Description = "قطع الغيار والصيانة" },
                    new Category { Name = "أدوية", Description = "الأدوية والمستلزمات الطبية" },
                    new Category { Name = "متنوعة", Description = "منتجات متنوعة أخرى" }
                };

                await context.Categories.AddRangeAsync(categories);
            }
        }

        /// <summary>
        /// إضافة المخازن الأساسية
        /// </summary>
        private static async Task SeedWarehousesAsync(InjazAccDbContext context)
        {
            if (!await context.Warehouses.AnyAsync())
            {
                var warehouses = new List<Warehouse>
                {
                    new Warehouse 
                    { 
                        Name = "المخزن الرئيسي", 
                        Code = "MAIN", 
                        Location = "المقر الرئيسي", 
                        Description = "المخزن الرئيسي للشركة",
                        IsActive = true
                    },
                    new Warehouse 
                    { 
                        Name = "مخزن الفرع الأول", 
                        Code = "BR01", 
                        Location = "الفرع الأول", 
                        Description = "مخزن الفرع الأول",
                        IsActive = true
                    },
                    new Warehouse 
                    { 
                        Name = "مخزن المردودات", 
                        Code = "RET", 
                        Location = "قسم المردودات", 
                        Description = "مخزن خاص بالمردودات والمنتجات المعيبة",
                        IsActive = true
                    }
                };

                await context.Warehouses.AddRangeAsync(warehouses);
            }
        }

        /// <summary>
        /// إضافة الحسابات الأساسية
        /// </summary>
        private static async Task SeedAccountsAsync(InjazAccDbContext context)
        {
            if (!await context.Accounts.AnyAsync())
            {
                var accounts = new List<Account>
                {
                    // الأصول
                    new Account { Code = "1", Name = "الأصول", Type = AccountType.Asset, IsParent = true },
                    new Account { Code = "11", Name = "الأصول المتداولة", Type = AccountType.Asset, IsParent = true, ParentAccountId = null },
                    new Account { Code = "111", Name = "النقدية والبنوك", Type = AccountType.Asset, IsParent = false },
                    new Account { Code = "112", Name = "العملاء", Type = AccountType.Asset, IsParent = false },
                    new Account { Code = "113", Name = "المخزون", Type = AccountType.Asset, IsParent = false },
                    new Account { Code = "114", Name = "المصروفات المدفوعة مقدماً", Type = AccountType.Asset, IsParent = false },

                    // الخصوم
                    new Account { Code = "2", Name = "الخصوم", Type = AccountType.Liability, IsParent = true },
                    new Account { Code = "21", Name = "الخصوم المتداولة", Type = AccountType.Liability, IsParent = true },
                    new Account { Code = "211", Name = "الموردين", Type = AccountType.Liability, IsParent = false },
                    new Account { Code = "212", Name = "المصروفات المستحقة", Type = AccountType.Liability, IsParent = false },
                    new Account { Code = "213", Name = "ضريبة القيمة المضافة", Type = AccountType.Liability, IsParent = false },

                    // حقوق الملكية
                    new Account { Code = "3", Name = "حقوق الملكية", Type = AccountType.Equity, IsParent = true },
                    new Account { Code = "31", Name = "رأس المال", Type = AccountType.Equity, IsParent = false },
                    new Account { Code = "32", Name = "الأرباح المحتجزة", Type = AccountType.Equity, IsParent = false },

                    // الإيرادات
                    new Account { Code = "4", Name = "الإيرادات", Type = AccountType.Revenue, IsParent = true },
                    new Account { Code = "41", Name = "إيرادات المبيعات", Type = AccountType.Revenue, IsParent = false },
                    new Account { Code = "42", Name = "إيرادات أخرى", Type = AccountType.Revenue, IsParent = false },

                    // المصروفات
                    new Account { Code = "5", Name = "المصروفات", Type = AccountType.Expense, IsParent = true },
                    new Account { Code = "51", Name = "تكلفة البضاعة المباعة", Type = AccountType.Expense, IsParent = false },
                    new Account { Code = "52", Name = "مصروفات التشغيل", Type = AccountType.Expense, IsParent = false },
                    new Account { Code = "53", Name = "مصروفات إدارية", Type = AccountType.Expense, IsParent = false },
                    new Account { Code = "54", Name = "مصروفات تسويقية", Type = AccountType.Expense, IsParent = false }
                };

                await context.Accounts.AddRangeAsync(accounts);
            }
        }

        /// <summary>
        /// إضافة معلومات الشركة الافتراضية
        /// </summary>
        private static async Task SeedCompanyInfoAsync(InjazAccDbContext context)
        {
            if (!await context.CompanyInfo.AnyAsync())
            {
                var companyInfo = new CompanyInfo
                {
                    Name = "شركة إنجاز للتجارة",
                    LegalName = "شركة إنجاز للتجارة المحدودة",
                    TaxNumber = "*********",
                    CommercialRegister = "**********",
                    Address = "الرياض، المملكة العربية السعودية",
                    Phone = "+966 11 123 4567",
                    Email = "<EMAIL>",
                    Website = "www.injazacc.com",
                    Currency = "ريال سعودي",
                    CurrencySymbol = "ر.س",
                    EstablishmentDate = DateTime.Now.AddYears(-5)
                };

                await context.CompanyInfo.AddAsync(companyInfo);
            }
        }

        /// <summary>
        /// إضافة السنة المالية الافتراضية
        /// </summary>
        private static async Task SeedFiscalYearAsync(InjazAccDbContext context)
        {
            if (!await context.FiscalYears.AnyAsync())
            {
                var currentYear = DateTime.Now.Year;
                var fiscalYear = new FiscalYear
                {
                    Name = $"السنة المالية {currentYear}",
                    StartDate = new DateTime(currentYear, 1, 1),
                    EndDate = new DateTime(currentYear, 12, 31),
                    IsCurrent = true,
                    IsClosed = false
                };

                await context.FiscalYears.AddAsync(fiscalYear);
            }
        }

        /// <summary>
        /// إضافة إعدادات النظام الافتراضية
        /// </summary>
        private static async Task SeedSystemSettingsAsync(InjazAccDbContext context)
        {
            if (!await context.SystemSettings.AnyAsync())
            {
                var settings = new List<SystemSettings>
                {
                    // إعدادات عامة
                    new SystemSettings { SettingKey = "DefaultLanguage", SettingValue = "ar", Description = "اللغة الافتراضية", Group = SettingGroup.General },
                    new SystemSettings { SettingKey = "DateFormat", SettingValue = "dd/MM/yyyy", Description = "تنسيق التاريخ", Group = SettingGroup.General },
                    new SystemSettings { SettingKey = "DecimalPlaces", SettingValue = "2", Description = "عدد الخانات العشرية", Group = SettingGroup.General },

                    // إعدادات الفواتير
                    new SystemSettings { SettingKey = "InvoicePrefix", SettingValue = "INV", Description = "بادئة رقم الفاتورة", Group = SettingGroup.Invoice },
                    new SystemSettings { SettingKey = "AutoInvoiceNumber", SettingValue = "true", Description = "ترقيم تلقائي للفواتير", Group = SettingGroup.Invoice },
                    new SystemSettings { SettingKey = "VATRate", SettingValue = "15", Description = "معدل ضريبة القيمة المضافة", Group = SettingGroup.Invoice },

                    // إعدادات المخزون
                    new SystemSettings { SettingKey = "LowStockAlert", SettingValue = "true", Description = "تنبيه نفاد المخزون", Group = SettingGroup.Inventory },
                    new SystemSettings { SettingKey = "LowStockThreshold", SettingValue = "10", Description = "حد التنبيه للمخزون", Group = SettingGroup.Inventory },

                    // إعدادات المحاسبة
                    new SystemSettings { SettingKey = "AutoJournalEntry", SettingValue = "true", Description = "إنشاء قيود تلقائية", Group = SettingGroup.Accounting },
                    new SystemSettings { SettingKey = "FiscalYearStart", SettingValue = "01/01", Description = "بداية السنة المالية", Group = SettingGroup.Accounting },

                    // إعدادات الأمان
                    new SystemSettings { SettingKey = "PasswordMinLength", SettingValue = "6", Description = "الحد الأدنى لطول كلمة المرور", Group = SettingGroup.Security },
                    new SystemSettings { SettingKey = "SessionTimeout", SettingValue = "30", Description = "انتهاء الجلسة (بالدقائق)", Group = SettingGroup.Security },

                    // إعدادات المظهر
                    new SystemSettings { SettingKey = "Theme", SettingValue = "Light", Description = "مظهر التطبيق", Group = SettingGroup.Appearance },
                    new SystemSettings { SettingKey = "PrimaryColor", SettingValue = "Teal", Description = "اللون الأساسي", Group = SettingGroup.Appearance }
                };

                await context.SystemSettings.AddRangeAsync(settings);
            }
        }
    }
}
