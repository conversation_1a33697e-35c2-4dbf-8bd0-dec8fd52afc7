using System;
using System.Windows;
using InjazAcc.Core.Models;

namespace InjazAcc.UI.Views.Customers
{
    /// <summary>
    /// Interaction logic for CustomerWindow.xaml
    /// </summary>
    public partial class CustomerWindow : Window
    {
    private bool _isEditMode = false;
    private Customer _customer;
    private readonly InjazAcc.DataAccess.InjazAccDbContext _dbContext;

        public Customer Customer => _customer;

        public CustomerWindow()
        {
            InitializeComponent();
            _dbContext = new InjazAcc.DataAccess.InjazAccDbContext();
            _customer = new Customer();
            InitializeControls();
        }

        public CustomerWindow(Customer customer)
        {
            InitializeComponent();
            _dbContext = new InjazAcc.DataAccess.InjazAccDbContext();
            _isEditMode = true;
            _customer = new Customer
            {
                Id = customer.Id,
                Code = customer.Code,
                Name = customer.Name,
                Phone = customer.Phone,
                Email = customer.Email,
                Address = customer.Address,
                OpeningBalance = customer.OpeningBalance
            };
            InitializeControls();
            LoadCustomerData();
        }

        private void InitializeControls()
        {
            // تعيين القيم الافتراضية
            if (!_isEditMode)
            {
                txtCode.Text = GenerateNewCode();
                txtBalance.Text = "0";
                txtCreditLimit.Text = "0";
                txtPaymentPeriod.Text = "30";
                cmbType.SelectedIndex = 0;
            }

            // تغيير عنوان النافذة في حالة التعديل
            if (_isEditMode)
            {
                txtWindowTitle.Text = "تعديل بيانات العميل";
                this.Title = "تعديل بيانات العميل";
            }
        }

        private string GenerateNewCode()
        {
            // توليد رمز جديد (في التطبيق الحقيقي سيتم جلب الرمز من قاعدة البيانات)
            Random random = new Random();
            return $"C{random.Next(1000, 9999)}";
        }

        private void LoadCustomerData()
        {
            // تحميل بيانات العميل في حالة التعديل
            if (_customer != null)
            {
                txtCode.Text = _customer.Code;
                txtName.Text = _customer.Name;
                txtPhone.Text = _customer.Phone;
                txtEmail.Text = _customer.Email;
                txtAddress.Text = _customer.Address;
                txtBalance.Text = _customer.OpeningBalance.ToString();
                txtCreditLimit.Text = "10000"; // قيمة افتراضية لحد الائتمان
                txtPaymentPeriod.Text = "30"; // قيمة افتراضية لفترة السداد
                
                // تحديد نوع العميل بناءً على الاسم (مجرد مثال)
                if (_customer.Name.Contains("شركة"))
                {
                    cmbType.SelectedIndex = 0; // شركة
                }
                else if (_customer.Name.Contains("مؤسسة"))
                {
                    cmbType.SelectedIndex = 1; // مؤسسة
                }
                else
                {
                    cmbType.SelectedIndex = 2; // فرد
                }
            }
        }

        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtCode.Text))
                {
                    MessageBox.Show("الرجاء إدخال رمز العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCode.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtName.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPhone.Text))
                {
                    MessageBox.Show("الرجاء إدخال رقم هاتف العميل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPhone.Focus();
                    return;
                }

                if (!double.TryParse(txtBalance.Text, out double balance))
                {
                    MessageBox.Show("الرجاء إدخال رصيد صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtBalance.Focus();
                    return;
                }

                if (!double.TryParse(txtCreditLimit.Text, out double creditLimit))
                {
                    MessageBox.Show("الرجاء إدخال حد ائتمان صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCreditLimit.Focus();
                    return;
                }

                if (!int.TryParse(txtPaymentPeriod.Text, out int paymentPeriod) || paymentPeriod < 0)
                {
                    MessageBox.Show("الرجاء إدخال فترة سداد صحيحة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtPaymentPeriod.Focus();
                    return;
                }

                // تحديث بيانات العميل
                _customer.Code = txtCode.Text;
                _customer.Name = txtName.Text;
                _customer.Phone = txtPhone.Text;
                _customer.Email = txtEmail.Text;
                _customer.Address = txtAddress.Text;
                _customer.OpeningBalance = (decimal)balance;

                // حفظ العميل في قاعدة البيانات
                if (_isEditMode)
                {
                    var existing = _dbContext.Customers.Find(_customer.Id);
                    if (existing != null)
                    {
                        existing.Code = _customer.Code;
                        existing.Name = _customer.Name;
                        existing.Phone = _customer.Phone;
                        existing.Email = _customer.Email;
                        existing.Address = _customer.Address;
                        existing.OpeningBalance = _customer.OpeningBalance;
                    }
                }
                else
                {
                    _dbContext.Customers.Add(_customer);
                }
                _dbContext.SaveChanges();

                // إغلاق النافذة بنجاح
                this.DialogResult = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            // إلغاء العملية وإغلاق النافذة
            this.DialogResult = false;
        }
    }
}
