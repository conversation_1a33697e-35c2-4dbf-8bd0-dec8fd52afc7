[2025-07-29 12:21:07] Exception Details:
Type: InjazAcc.Core.Exceptions.InjazAccException
Error Code: ERR-GEN-002
Severity: Error
Message: حدث خطأ أثناء تحميل الصفحة
Stack Trace: 
Inner Exception:
Type: System.Windows.Markup.XamlParseException
Message: 'Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.' Line number '240' and line position '54'.
Stack Trace:    at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xaml<PERSON><PERSON>er, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at InjazAcc.UI.Views.Settings.SettingsPage.InitializeComponent() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml:line 1
   at InjazAcc.UI.Views.Settings.SettingsPage..ctor() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml.cs:line 108
   at InjazAcc.UI.MainWindow.menuItem_Selected(Object sender, RoutedEventArgs e) in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\MainWindow.xaml.cs:line 121
--------------------------------------------------------------------------------
[2025-07-29 12:21:14] Exception Details:
Type: InjazAcc.Core.Exceptions.InjazAccException
Error Code: ERR-GEN-002
Severity: Error
Message: حدث خطأ أثناء تحميل الصفحة
Stack Trace: 
Inner Exception:
Type: System.Windows.Markup.XamlParseException
Message: 'Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.' Line number '240' and line position '54'.
Stack Trace:    at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at InjazAcc.UI.Views.Settings.SettingsPage.InitializeComponent() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml:line 1
   at InjazAcc.UI.Views.Settings.SettingsPage..ctor() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml.cs:line 108
   at InjazAcc.UI.MainWindow.menuItem_Selected(Object sender, RoutedEventArgs e) in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\MainWindow.xaml.cs:line 121
--------------------------------------------------------------------------------
[2025-07-29 12:21:26] Exception Details:
Type: InjazAcc.Core.Exceptions.InjazAccException
Error Code: ERR-GEN-002
Severity: Error
Message: حدث خطأ أثناء تحميل الصفحة
Stack Trace: 
Inner Exception:
Type: System.Windows.Markup.XamlParseException
Message: 'Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.' Line number '240' and line position '54'.
Stack Trace:    at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at InjazAcc.UI.Views.Settings.SettingsPage.InitializeComponent() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml:line 1
   at InjazAcc.UI.Views.Settings.SettingsPage..ctor() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml.cs:line 108
   at InjazAcc.UI.MainWindow.menuItem_Selected(Object sender, RoutedEventArgs e) in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\MainWindow.xaml.cs:line 121
--------------------------------------------------------------------------------
[2025-07-29 12:22:57] Exception Details:
Type: InjazAcc.Core.Exceptions.InjazAccException
Error Code: ERR-GEN-002
Severity: Error
Message: حدث خطأ أثناء تحميل الصفحة
Stack Trace: 
Inner Exception:
Type: System.Windows.Markup.XamlParseException
Message: 'Provide value on 'System.Windows.Baml2006.TypeConverterMarkupExtension' threw an exception.' Line number '240' and line position '54'.
Stack Trace:    at System.Windows.Markup.XamlReader.RewrapException(Exception e, IXamlLineInfo lineInfo, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.Load(XamlReader xamlReader, IXamlObjectWriterFactory writerFactory, Boolean skipJournaledProperties, Object rootObject, XamlObjectWriterSettings settings, Uri baseUri)
   at System.Windows.Markup.WpfXamlLoader.LoadBaml(XamlReader xamlReader, Boolean skipJournaledProperties, Object rootObject, XamlAccessLevel accessLevel, Uri baseUri)
   at System.Windows.Markup.XamlReader.LoadBaml(Stream stream, ParserContext parserContext, Object parent, Boolean closeStream)
   at System.Windows.Application.LoadComponent(Object component, Uri resourceLocator)
   at InjazAcc.UI.Views.Settings.SettingsPage.InitializeComponent() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml:line 1
   at InjazAcc.UI.Views.Settings.SettingsPage..ctor() in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\Views\Settings\SettingsPage.xaml.cs:line 108
   at InjazAcc.UI.MainWindow.menuItem_Selected(Object sender, RoutedEventArgs e) in C:\Users\<USER>\Desktop\injaz_acc\injaz_acc\src\InjazAcc.UI\MainWindow.xaml.cs:line 121
--------------------------------------------------------------------------------
