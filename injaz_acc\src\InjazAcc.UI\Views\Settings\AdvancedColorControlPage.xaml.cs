using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using InjazAcc.UI.Helpers;
using InjazAcc.UI.Views.Shared;

namespace InjazAcc.UI.Views.Settings
{
    /// <summary>
    /// صفحة التحكم المتقدم بالألوان
    /// </summary>
    public partial class AdvancedColorControlPage : UserControl
    {
        /// <summary>
        /// حدث تغيير الألوان
        /// </summary>
        public event EventHandler ColorsChanged;

        public AdvancedColorControlPage()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        /// <summary>
        /// إثارة حدث تغيير الألوان
        /// </summary>
        protected virtual void OnColorsChanged()
        {
            ColorsChanged?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// تحميل الإعدادات الحالية
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                DataContext = settings;
                
                // تحديث ألوان الأزرار
                UpdateButtonColors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث ألوان الأزرار
        /// </summary>
        private void UpdateButtonColors()
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                
                // تحديث ألوان الأزرار الأساسية
                UpdateButtonColor(btnPrimaryColor, settings.PrimaryColor);
                UpdateButtonColor(btnPrimaryDarkColor, settings.PrimaryDarkColor);
                UpdateButtonColor(btnPrimaryLightColor, settings.PrimaryLightColor);
                
                UpdateButtonColor(btnSecondaryColor, settings.SecondaryColor);
                UpdateButtonColor(btnSecondaryDarkColor, settings.SecondaryDarkColor);
                UpdateButtonColor(btnSecondaryLightColor, settings.SecondaryLightColor);
                
                UpdateButtonColor(btnAccentColor, settings.AccentColor);
                
                // تحديث ألوان الخلفية
                UpdateButtonColor(btnBackgroundColor, settings.BackgroundColor);
                UpdateButtonColor(btnSurfaceColor, settings.SurfaceColor);
                UpdateButtonColor(btnCardBackgroundColor, settings.CardBackgroundColor);
                UpdateButtonColor(btnDialogBackgroundColor, settings.DialogBackgroundColor);
                UpdateButtonColor(btnSidebarBackgroundColor, settings.SidebarBackgroundColor);
                UpdateButtonColor(btnHeaderBackgroundColor, settings.HeaderBackgroundColor);
                UpdateButtonColor(btnFooterBackgroundColor, settings.FooterBackgroundColor);
                
                // تحديث ألوان النصوص
                UpdateButtonColor(btnPrimaryTextColor, settings.PrimaryTextColor);
                UpdateButtonColor(btnSecondaryTextColor, settings.SecondaryTextColor);
                UpdateButtonColor(btnDisabledTextColor, settings.DisabledTextColor);
                UpdateButtonColor(btnHintTextColor, settings.HintTextColor);
                UpdateButtonColor(btnOnPrimaryTextColor, settings.OnPrimaryTextColor);
                UpdateButtonColor(btnOnSecondaryTextColor, settings.OnSecondaryTextColor);
                UpdateButtonColor(btnOnSurfaceTextColor, settings.OnSurfaceTextColor);
                UpdateButtonColor(btnOnBackgroundTextColor, settings.OnBackgroundTextColor);
                
                // تحديث ألوان الحدود
                UpdateButtonColor(btnBorderColor, settings.BorderColor);
                UpdateButtonColor(btnDividerColor, settings.DividerColor);
                UpdateButtonColor(btnOutlineColor, settings.OutlineColor);
                UpdateButtonColor(btnFocusBorderColor, settings.FocusBorderColor);
                UpdateButtonColor(btnErrorBorderColor, settings.ErrorBorderColor);
                
                // تحديث ألوان الأزرار
                UpdateButtonColor(btnButtonBackgroundColor, settings.ButtonBackgroundColor);
                UpdateButtonColor(btnButtonTextColor, settings.ButtonTextColor);
                UpdateButtonColor(btnButtonHoverColor, settings.ButtonHoverColor);
                UpdateButtonColor(btnButtonPressedColor, settings.ButtonPressedColor);
                UpdateButtonColor(btnButtonDisabledColor, settings.ButtonDisabledColor);
                UpdateButtonColor(btnButtonDisabledTextColor, settings.ButtonDisabledTextColor);
                
                UpdateButtonColor(btnSecondaryButtonBackgroundColor, settings.SecondaryButtonBackgroundColor);
                UpdateButtonColor(btnSecondaryButtonTextColor, settings.SecondaryButtonTextColor);
                UpdateButtonColor(btnSecondaryButtonHoverColor, settings.SecondaryButtonHoverColor);
                UpdateButtonColor(btnSecondaryButtonPressedColor, settings.SecondaryButtonPressedColor);
                
                // تحديث ألوان الحالة
                UpdateButtonColor(btnSelectionColor, settings.SelectionColor);
                UpdateButtonColor(btnHoverColor, settings.HoverColor);
                UpdateButtonColor(btnFocusColor, settings.FocusColor);
                UpdateButtonColor(btnActiveColor, settings.ActiveColor);
                UpdateButtonColor(btnHighlightColor, settings.HighlightColor);
                
                UpdateButtonColor(btnSuccessColor, settings.SuccessColor);
                UpdateButtonColor(btnSuccessLightColor, settings.SuccessLightColor);
                UpdateButtonColor(btnSuccessDarkColor, settings.SuccessDarkColor);
                
                UpdateButtonColor(btnWarningColor, settings.WarningColor);
                UpdateButtonColor(btnWarningLightColor, settings.WarningLightColor);
                UpdateButtonColor(btnWarningDarkColor, settings.WarningDarkColor);
                
                UpdateButtonColor(btnErrorColor, settings.ErrorColor);
                UpdateButtonColor(btnErrorLightColor, settings.ErrorLightColor);
                UpdateButtonColor(btnErrorDarkColor, settings.ErrorDarkColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث ألوان الأزرار: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث لون زر واحد
        /// </summary>
        private void UpdateButtonColor(Button button, string colorValue)
        {
            try
            {
                if (button != null && !string.IsNullOrEmpty(colorValue))
                {
                    var color = (Color)ColorConverter.ConvertFromString(colorValue);
                    button.Background = new SolidColorBrush(color);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث لون الزر: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج النقر على أزرار الألوان
        /// </summary>
        private void ColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is string colorProperty)
                {
                    var colorPicker = new ColorPickerWindow();
                    
                    // تعيين اللون الحالي
                    if (button.Background is SolidColorBrush brush)
                    {
                        colorPicker.SelectedColor = brush.Color;
                    }
                    
                    if (colorPicker.ShowDialog() == true)
                    {
                        var newColor = colorPicker.SelectedColor.ToString();
                        
                        // تحديث اللون في المدير
                        AdvancedColorManager.UpdateColor(colorProperty, newColor);
                        
                        // تحديث الزر
                        button.Background = new SolidColorBrush(colorPicker.SelectedColor);
                        
                        // تحديث مربع النص المقابل
                        UpdateTextBoxValue(colorProperty, newColor);
                        
                        // تطبيق الألوان
                        AdvancedColorManager.ApplyAllColors();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار اللون: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج تغيير النص في مربعات الألوان
        /// </summary>
        private void ColorTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (sender is TextBox textBox && textBox.Tag is string colorProperty)
                {
                    var colorValue = textBox.Text;
                    
                    if (IsValidColor(colorValue))
                    {
                        // تحديث اللون في المدير
                        AdvancedColorManager.UpdateColor(colorProperty, colorValue);
                        
                        // تحديث الزر المقابل
                        UpdateButtonColorByProperty(colorProperty, colorValue);
                        
                        // تطبيق الألوان
                        AdvancedColorManager.ApplyAllColors();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث اللون من النص: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث قيمة مربع النص
        /// </summary>
        private void UpdateTextBoxValue(string colorProperty, string colorValue)
        {
            try
            {
                var textBoxName = $"txt{colorProperty}";
                var textBox = FindName(textBoxName) as TextBox;
                if (textBox != null)
                {
                    textBox.Text = colorValue;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مربع النص: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث لون الزر بناءً على خاصية اللون
        /// </summary>
        private void UpdateButtonColorByProperty(string colorProperty, string colorValue)
        {
            try
            {
                var buttonName = $"btn{colorProperty}";
                var button = FindName(buttonName) as Button;
                if (button != null)
                {
                    UpdateButtonColor(button, colorValue);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث لون الزر: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة اللون
        /// </summary>
        private bool IsValidColor(string colorValue)
        {
            try
            {
                ColorConverter.ConvertFromString(colorValue);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// تطبيق قالب ألوان
        /// </summary>
        private void ApplyTheme_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is string themeName)
                {
                    AdvancedColorManager.ApplyColorTheme(themeName);
                    LoadCurrentSettings();
                    
                    MessageBox.Show($"تم تطبيق قالب '{button.Content}' بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق القالب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ الإعدادات
        /// </summary>
        private void SaveSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AdvancedColorManager.SaveSettings();
                MessageBox.Show("تم حفظ الإعدادات بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل الإعدادات
        /// </summary>
        private void LoadSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AdvancedColorManager.LoadSettings();
                AdvancedColorManager.ApplyAllColors();
                LoadCurrentSettings();
                
                MessageBox.Show("تم تحميل الإعدادات بنجاح!", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات
        /// </summary>
        private void ResetSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من إعادة تعيين جميع الألوان إلى القيم الافتراضية؟", 
                    "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    AdvancedColorManager.ResetToDefaults();
                    LoadCurrentSettings();
                    
                    MessageBox.Show("تم إعادة تعيين الألوان بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة التعيين: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تصدير الألوان
        /// </summary>
        private void ExportColors_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "ملفات الألوان (*.json)|*.json|جميع الملفات (*.*)|*.*",
                    DefaultExt = "json",
                    FileName = "InjazAcc_Colors.json"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var colors = AdvancedColorManager.GetAllColors();
                    var json = System.Text.Json.JsonSerializer.Serialize(colors, new System.Text.Json.JsonSerializerOptions 
                    { 
                        WriteIndented = true 
                    });
                    
                    File.WriteAllText(saveDialog.FileName, json);
                    
                    MessageBox.Show("تم تصدير الألوان بنجاح!", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// استيراد الألوان
        /// </summary>
        private void ImportColors_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = "ملفات الألوان (*.json)|*.json|جميع الملفات (*.*)|*.*",
                    DefaultExt = "json"
                };

                if (openDialog.ShowDialog() == true)
                {
                    var json = File.ReadAllText(openDialog.FileName);
                    var colors = System.Text.Json.JsonSerializer.Deserialize<System.Collections.Generic.Dictionary<string, string>>(json);
                    
                    if (colors != null)
                    {
                        foreach (var color in colors)
                        {
                            AdvancedColorManager.UpdateColor(color.Key, color.Value);
                        }
                        
                        AdvancedColorManager.ApplyAllColors();
                        LoadCurrentSettings();
                        
                        MessageBox.Show("تم استيراد الألوان بنجاح!", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد الألوان: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}