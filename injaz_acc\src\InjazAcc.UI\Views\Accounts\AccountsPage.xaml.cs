using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for AccountsPage.xaml
    /// </summary>
    public partial class AccountsPage : Page
    {
        public AccountsPage()
        {
            InitializeComponent();
        }

        private void OpeningBalances_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new OpeningBalancesPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة أرصدة أول المدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Cash_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new CashPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة الخزينة/النقدية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FinancialStatements_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new FinancialStatementsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة الحسابات الختامية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Ledger_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new LedgerPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة حساب الأستاذ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ChartOfAccounts_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new ChartOfAccountsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة دليل الحسابات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FinancialReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                NavigationService.Navigate(new FinancialReportsPage());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح صفحة التقارير المالية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أزرار إضافية لصفحة الحسابات
        private void btnAddAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة إضافة حساب جديد", "إضافة حساب", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة إضافة حساب جديد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnEditAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة تعديل الحساب المحدد", "تعديل حساب", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة تعديل الحساب المحدد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف الحساب المحدد؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    MessageBox.Show("تم حذف الحساب بنجاح", "حذف حساب", MessageBoxButton.OK, MessageBoxImage.Information);
                    // TODO: تنفيذ عملية الحذف
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnAccountStatement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح كشف حساب للحساب المحدد", "كشف حساب", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح كشف حساب للحساب المحدد
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnJournalEntry_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح نافذة إدخال قيد محاسبي", "قيد محاسبي", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح نافذة إدخال قيد محاسبي
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnTrialBalance_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح ميزان المراجعة", "ميزان المراجعة", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح ميزان المراجعة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnIncomeStatement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح قائمة الدخل", "قائمة الدخل", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح قائمة الدخل
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBalanceSheet_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح الميزانية العمومية", "الميزانية العمومية", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح الميزانية العمومية
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnCashFlow_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم فتح قائمة التدفقات النقدية", "التدفقات النقدية", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: فتح قائمة التدفقات النقدية
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackupAccounts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم إنشاء نسخة احتياطية من الحسابات", "نسخة احتياطية", MessageBoxButton.OK, MessageBoxImage.Information);
                // TODO: إنشاء نسخة احتياطية من الحسابات
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
