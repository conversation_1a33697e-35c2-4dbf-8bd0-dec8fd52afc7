using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using InjazAcc.Core.Models;
using InjazAcc.Services;

namespace TestNewExcelDesign
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("اختبار التصميم الجديد لتصدير Excel");
            Console.WriteLine("=====================================");

            // إنشاء بيانات تجريبية للعملاء
            var customers = new List<Customer>
            {
                new Customer
                {
                    Code = "C001",
                    Name = "شركة الأهرام للتجارة العامة",
                    ContactPerson = "أحمد محمد علي",
                    Phone = "01234567890",
                    Email = "<EMAIL>",
                    Address = "شارع النيل، القاهرة الجديدة",
                    CreditLimit = 50000,
                    OpeningBalance = 25000
                },
                new Customer
                {
                    Code = "C002",
                    Name = "مؤسسة النور للمقاولات والإنشاءات",
                    ContactPerson = "فاطمة أحمد حسن",
                    Phone = "01098765432",
                    Email = "<EMAIL>",
                    Address = "شارع الجمهورية، الإسكندرية",
                    CreditLimit = 75000,
                    OpeningBalance = 45000
                },
                new Customer
                {
                    Code = "C003",
                    Name = "شركة الفجر للصناعات الغذائية",
                    ContactPerson = "محمد حسن إبراهيم",
                    Phone = "01555123456",
                    Email = "<EMAIL>",
                    Address = "المنطقة الصناعية، الجيزة",
                    CreditLimit = 100000,
                    OpeningBalance = 80000
                }
            };

            // إنشاء بيانات تجريبية للموردين
            var suppliers = new List<Supplier>
            {
                new Supplier
                {
                    Code = "S001",
                    Name = "شركة المواد الخام المتقدمة",
                    ContactPerson = "خالد عبدالله",
                    Phone = "01111222333",
                    Email = "<EMAIL>",
                    Address = "المنطقة الصناعية، القاهرة",
                    CreditLimit = 80000,
                    OpeningBalance = 35000
                },
                new Supplier
                {
                    Code = "S002",
                    Name = "مؤسسة التوريدات الشاملة",
                    ContactPerson = "سارة محمود",
                    Phone = "01222333444",
                    Email = "<EMAIL>",
                    Address = "شارع الصناعة، الإسكندرية",
                    CreditLimit = 60000,
                    OpeningBalance = 28000
                }
            };

            try
            {
                Console.WriteLine("جاري تصدير تقرير العملاء...");
                string customersFile = $@"c:\Users\<USER>\Desktop\injaz_acc\تقرير_العملاء_الجديد_{DateTime.Now:yyyy-MM-dd-HH-mm}.xls";
                bool customersSuccess = await AlternativeExcelExportService.ExportCustomersToExcel(customers, customersFile);
                
                if (customersSuccess)
                {
                    Console.WriteLine($"✅ تم تصدير تقرير العملاء بنجاح: {customersFile}");
                }
                else
                {
                    Console.WriteLine("❌ فشل في تصدير تقرير العملاء");
                }

                Console.WriteLine("\nجاري تصدير تقرير الموردين...");
                string suppliersFile = $@"c:\Users\<USER>\Desktop\injaz_acc\تقرير_الموردين_الجديد_{DateTime.Now:yyyy-MM-dd-HH-mm}.xls";
                bool suppliersSuccess = await AlternativeExcelExportService.ExportSuppliersToExcel(suppliers, suppliersFile);
                
                if (suppliersSuccess)
                {
                    Console.WriteLine($"✅ تم تصدير تقرير الموردين بنجاح: {suppliersFile}");
                }
                else
                {
                    Console.WriteLine("❌ فشل في تصدير تقرير الموردين");
                }

                Console.WriteLine("\n🎉 تم الانتهاء من الاختبار!");
                Console.WriteLine("يمكنك الآن فتح الملفات المُصدرة للتحقق من التصميم الجديد.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ حدث خطأ أثناء الاختبار: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
            }

            Console.WriteLine("\nاضغط أي مفتاح للخروج...");
            Console.ReadKey();
        }
    }
}