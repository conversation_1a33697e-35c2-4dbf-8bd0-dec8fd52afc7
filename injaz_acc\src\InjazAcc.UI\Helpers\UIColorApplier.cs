using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Media;
using System.Linq;

namespace InjazAcc.UI.Helpers
{
    /// <summary>
    /// مطبق الألوان على عناصر الواجهة
    /// </summary>
    public static class UIColorApplier
    {
        /// <summary>
        /// تطبيق الألوان على جميع النوافذ المفتوحة
        /// </summary>
        public static void ApplyColorsToAllWindows()
        {
            try
            {
                foreach (Window window in Application.Current.Windows)
                {
                    ApplyColorsToWindow(window);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الألوان على النوافذ: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق الألوان على نافذة واحدة
        /// </summary>
        public static void ApplyColorsToWindow(Window window)
        {
            try
            {
                if (window == null) return;

                // تطبيق الألوان على النافذة الرئيسية
                ApplyColorsToElement(window);

                // تطبيق الألوان على جميع العناصر الفرعية
                ApplyColorsToChildren(window);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الألوان على النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق الألوان على عنصر واحد
        /// </summary>
        public static void ApplyColorsToElement(FrameworkElement element)
        {
            try
            {
                if (element == null) return;

                var settings = AdvancedColorManager.CurrentSettings;

                // تطبيق الألوان حسب نوع العنصر
                switch (element)
                {
                    case Window window:
                        ApplyWindowColors(window, settings);
                        break;
                    case Page page:
                        ApplyPageColors(page, settings);
                        break;
                    case UserControl userControl:
                        ApplyUserControlColors(userControl, settings);
                        break;
                    case Button button:
                        ApplyButtonColors(button, settings);
                        break;
                    case TextBox textBox:
                        ApplyTextBoxColors(textBox, settings);
                        break;
                    case ComboBox comboBox:
                        ApplyComboBoxColors(comboBox, settings);
                        break;
                    case DataGrid dataGrid:
                        ApplyDataGridColors(dataGrid, settings);
                        break;
                    case Border border:
                        ApplyBorderColors(border, settings);
                        break;
                    case TextBlock textBlock:
                        ApplyTextBlockColors(textBlock, settings);
                        break;
                    case GroupBox groupBox:
                        ApplyGroupBoxColors(groupBox, settings);
                        break;
                    case TabControl tabControl:
                        ApplyTabControlColors(tabControl, settings);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الألوان على العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق الألوان على العناصر الفرعية
        /// </summary>
        private static void ApplyColorsToChildren(DependencyObject parent)
        {
            try
            {
                int childCount = VisualTreeHelper.GetChildrenCount(parent);
                for (int i = 0; i < childCount; i++)
                {
                    var child = VisualTreeHelper.GetChild(parent, i);
                    
                    if (child is FrameworkElement element)
                    {
                        ApplyColorsToElement(element);
                    }
                    
                    ApplyColorsToChildren(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الألوان على العناصر الفرعية: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان النافذة
        /// </summary>
        private static void ApplyWindowColors(Window window, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                window.Background = CreateBrush(settings.BackgroundColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان النافذة: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان الصفحة
        /// </summary>
        private static void ApplyPageColors(Page page, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                page.Background = CreateBrush(settings.BackgroundColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان الصفحة: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان عنصر التحكم المخصص
        /// </summary>
        private static void ApplyUserControlColors(UserControl userControl, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                if (userControl.Background == null || userControl.Background == Brushes.Transparent)
                {
                    userControl.Background = CreateBrush(settings.SurfaceColor);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان عنصر التحكم: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان الأزرار
        /// </summary>
        private static void ApplyButtonColors(Button button, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                // تحديد نوع الزر بناءً على الاسم أو الخصائص
                if (IsSecondaryButton(button))
                {
                    button.Background = CreateBrush(settings.SecondaryButtonBackgroundColor);
                    button.Foreground = CreateBrush(settings.SecondaryButtonTextColor);
                }
                else if (IsPrimaryButton(button))
                {
                    button.Background = CreateBrush(settings.ButtonBackgroundColor);
                    button.Foreground = CreateBrush(settings.ButtonTextColor);
                }
                
                button.BorderBrush = CreateBrush(settings.BorderColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان الزر: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان مربعات النص
        /// </summary>
        private static void ApplyTextBoxColors(TextBox textBox, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                textBox.Background = CreateBrush(settings.SurfaceColor);
                textBox.Foreground = CreateBrush(settings.PrimaryTextColor);
                textBox.BorderBrush = CreateBrush(settings.BorderColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان مربع النص: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان القوائم المنسدلة
        /// </summary>
        private static void ApplyComboBoxColors(ComboBox comboBox, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                comboBox.Background = CreateBrush(settings.SurfaceColor);
                comboBox.Foreground = CreateBrush(settings.PrimaryTextColor);
                comboBox.BorderBrush = CreateBrush(settings.BorderColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان القائمة المنسدلة: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان الجداول
        /// </summary>
        private static void ApplyDataGridColors(DataGrid dataGrid, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                dataGrid.Background = CreateBrush(settings.SurfaceColor);
                dataGrid.Foreground = CreateBrush(settings.PrimaryTextColor);
                dataGrid.BorderBrush = CreateBrush(settings.TableBorderColor);
                
                // تطبيق ألوان الرأس
                if (dataGrid.ColumnHeaderStyle == null)
                {
                    dataGrid.ColumnHeaderStyle = new Style(typeof(System.Windows.Controls.Primitives.DataGridColumnHeader));
                }
                dataGrid.ColumnHeaderStyle.Setters.Add(new Setter(Control.BackgroundProperty, CreateBrush(settings.TableHeaderColor)));
                dataGrid.ColumnHeaderStyle.Setters.Add(new Setter(Control.ForegroundProperty, CreateBrush(settings.PrimaryTextColor)));
                
                // تطبيق ألوان الصفوف
                if (dataGrid.RowStyle == null)
                {
                    dataGrid.RowStyle = new Style(typeof(DataGridRow));
                }
                dataGrid.RowStyle.Setters.Add(new Setter(Control.BackgroundProperty, CreateBrush(settings.TableRowEvenColor)));
                
                // تطبيق ألوان الصف المحدد
                if (dataGrid.CellStyle == null)
                {
                    dataGrid.CellStyle = new Style(typeof(DataGridCell));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان الجدول: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان الحدود
        /// </summary>
        private static void ApplyBorderColors(Border border, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                if (border.Background == null || border.Background == Brushes.Transparent)
                {
                    border.Background = CreateBrush(settings.CardBackgroundColor);
                }
                border.BorderBrush = CreateBrush(settings.BorderColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان الحدود: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان النصوص
        /// </summary>
        private static void ApplyTextBlockColors(TextBlock textBlock, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                if (textBlock.Foreground == null || textBlock.Foreground == Brushes.Black)
                {
                    // تحديد نوع النص بناءً على الخصائص
                    if (IsSecondaryText(textBlock))
                    {
                        textBlock.Foreground = CreateBrush(settings.SecondaryTextColor);
                    }
                    else if (IsHintText(textBlock))
                    {
                        textBlock.Foreground = CreateBrush(settings.HintTextColor);
                    }
                    else
                    {
                        textBlock.Foreground = CreateBrush(settings.PrimaryTextColor);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان النص: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان مجموعات العناصر
        /// </summary>
        private static void ApplyGroupBoxColors(GroupBox groupBox, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                groupBox.Foreground = CreateBrush(settings.PrimaryTextColor);
                groupBox.BorderBrush = CreateBrush(settings.BorderColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان مجموعة العناصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان التبويبات
        /// </summary>
        private static void ApplyTabControlColors(TabControl tabControl, AdvancedColorManager.AdvancedColorSettings settings)
        {
            try
            {
                tabControl.Background = CreateBrush(settings.SurfaceColor);
                tabControl.Foreground = CreateBrush(settings.PrimaryTextColor);
                tabControl.BorderBrush = CreateBrush(settings.BorderColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق ألوان التبويبات: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء فرشاة من لون
        /// </summary>
        private static SolidColorBrush CreateBrush(string colorValue)
        {
            try
            {
                var color = (Color)ColorConverter.ConvertFromString(colorValue);
                return new SolidColorBrush(color);
            }
            catch
            {
                return Brushes.Transparent;
            }
        }

        /// <summary>
        /// تحديد ما إذا كان الزر ثانوي
        /// </summary>
        private static bool IsSecondaryButton(Button button)
        {
            var name = button.Name?.ToLower() ?? "";
            var content = button.Content?.ToString()?.ToLower() ?? "";
            
            return name.Contains("secondary") || name.Contains("cancel") || name.Contains("close") ||
                   content.Contains("إلغاء") || content.Contains("إغلاق") || content.Contains("رجوع");
        }

        /// <summary>
        /// تحديد ما إذا كان الزر أساسي
        /// </summary>
        private static bool IsPrimaryButton(Button button)
        {
            var name = button.Name?.ToLower() ?? "";
            var content = button.Content?.ToString()?.ToLower() ?? "";
            
            return name.Contains("primary") || name.Contains("save") || name.Contains("ok") ||
                   content.Contains("حفظ") || content.Contains("موافق") || content.Contains("تأكيد");
        }

        /// <summary>
        /// تحديد ما إذا كان النص ثانوي
        /// </summary>
        private static bool IsSecondaryText(TextBlock textBlock)
        {
            var name = textBlock.Name?.ToLower() ?? "";
            var text = textBlock.Text?.ToLower() ?? "";
            
            return name.Contains("secondary") || name.Contains("subtitle") || name.Contains("description") ||
                   textBlock.FontSize < 14 || textBlock.Opacity < 1.0;
        }

        /// <summary>
        /// تحديد ما إذا كان النص تلميح
        /// </summary>
        private static bool IsHintText(TextBlock textBlock)
        {
            var name = textBlock.Name?.ToLower() ?? "";
            
            return name.Contains("hint") || name.Contains("placeholder") || name.Contains("help");
        }

        /// <summary>
        /// تطبيق ألوان الأقسام على عنصر
        /// </summary>
        public static void ApplySectionColor(FrameworkElement element, string sectionName)
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                string colorValue = sectionName.ToLower() switch
                {
                    "sales" or "مبيعات" => settings.SalesColor,
                    "purchases" or "مشتريات" => settings.PurchasesColor,
                    "inventory" or "مخزون" => settings.InventoryColor,
                    "customers" or "عملاء" => settings.CustomersColor,
                    "suppliers" or "موردين" => settings.SuppliersColor,
                    "accounting" or "محاسبة" => settings.AccountingColor,
                    "reports" or "تقارير" => settings.ReportsColor,
                    "settings" or "إعدادات" => settings.SettingsColor,
                    _ => settings.PrimaryColor
                };

                if (element is Control control)
                {
                    control.Background = CreateBrush(colorValue);
                    control.Foreground = CreateBrush(settings.OnPrimaryTextColor);
                }
                else if (element is Border border)
                {
                    border.Background = CreateBrush(colorValue);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق لون القسم: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق ألوان الحالة على عنصر
        /// </summary>
        public static void ApplyStatusColor(FrameworkElement element, string status)
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                string colorValue = status.ToLower() switch
                {
                    "success" or "نجح" or "مكتمل" => settings.SuccessColor,
                    "warning" or "تحذير" or "انتظار" => settings.WarningColor,
                    "error" or "خطأ" or "فشل" => settings.ErrorColor,
                    "info" or "معلومات" => settings.InfoColor,
                    _ => settings.PrimaryColor
                };

                if (element is Control control)
                {
                    control.Background = CreateBrush(colorValue);
                    control.Foreground = CreateBrush(settings.OnPrimaryTextColor);
                }
                else if (element is Border border)
                {
                    border.Background = CreateBrush(colorValue);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق لون الحالة: {ex.Message}");
            }
        }
    }
}