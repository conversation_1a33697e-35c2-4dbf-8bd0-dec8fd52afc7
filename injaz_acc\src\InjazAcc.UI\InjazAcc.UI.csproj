﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\InjazAcc.Core\InjazAcc.Core.csproj" />
    <ProjectReference Include="..\InjazAcc.Services\InjazAcc.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.7" />
  </ItemGroup>



  <ItemGroup>
    <Resource Include="Resources\user.png" />
  </ItemGroup>

  <PropertyGroup>
    <EnableDefaultCompileItems>true</EnableDefaultCompileItems>
    <EnableDefaultPageItems>true</EnableDefaultPageItems>
  </PropertyGroup>

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

</Project>
