<Window x:Class="InjazAcc.UI.Views.Shared.AdvancedColorPickerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="منتقي الألوان المتقدم" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource BackgroundBrush}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="اختيار اللون" 
                   FontSize="24" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"
                   Foreground="{DynamicResource PrimaryTextBrush}"/>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- منطقة اختيار اللون -->
            <Grid Grid.Column="0" Margin="0,0,20,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="300"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- عجلة الألوان -->
                <Border Grid.Row="0" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="5">
                    <Canvas x:Name="colorWheel" Background="White" MouseLeftButtonDown="ColorWheel_MouseLeftButtonDown" MouseMove="ColorWheel_MouseMove">
                        <!-- سيتم رسم عجلة الألوان هنا برمجياً -->
                    </Canvas>
                </Border>

                <!-- شريط السطوع -->
                <Grid Grid.Row="1" Margin="0,10,0,0" Height="30">
                    <Border BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="3">
                        <Canvas x:Name="brightnessBar" MouseLeftButtonDown="BrightnessBar_MouseLeftButtonDown" MouseMove="BrightnessBar_MouseMove">
                            <!-- سيتم رسم شريط السطوع هنا برمجياً -->
                        </Canvas>
                    </Border>
                </Grid>

                <!-- شريط الشفافية -->
                <Grid Grid.Row="2" Margin="0,10,0,0" Height="30">
                    <Border BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="3">
                        <Canvas x:Name="alphaBar" MouseLeftButtonDown="AlphaBar_MouseLeftButtonDown" MouseMove="AlphaBar_MouseMove">
                            <!-- سيتم رسم شريط الشفافية هنا برمجياً -->
                        </Canvas>
                    </Border>
                </Grid>
            </Grid>

            <!-- لوحة التحكم -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- معاينة اللون -->
                <GroupBox Grid.Row="0" Header="معاينة اللون" Margin="0,0,0,15">
                    <Grid Height="80">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <Border Grid.Column="0" x:Name="currentColorPreview" 
                                Background="{Binding SelectedColor, RelativeSource={RelativeSource AncestorType=Window}}"
                                BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="3" Margin="0,0,5,0"/>
                        
                        <Border Grid.Column="1" x:Name="originalColorPreview" 
                                Background="{Binding OriginalColor, RelativeSource={RelativeSource AncestorType=Window}}"
                                BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" CornerRadius="3" Margin="5,0,0,0"/>
                    </Grid>
                </GroupBox>

                <!-- قيم RGB -->
                <GroupBox Grid.Row="1" Header="RGB" Margin="0,0,0,15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="R:" VerticalAlignment="Center" Margin="0,0,10,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtRed" Margin="0,0,0,5" TextChanged="RgbTextBox_TextChanged"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="G:" VerticalAlignment="Center" Margin="0,0,10,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtGreen" Margin="0,0,0,5" TextChanged="RgbTextBox_TextChanged"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="B:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtBlue" TextChanged="RgbTextBox_TextChanged"/>
                    </Grid>
                </GroupBox>

                <!-- قيم HSV -->
                <GroupBox Grid.Row="2" Header="HSV" Margin="0,0,0,15">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="H:" VerticalAlignment="Center" Margin="0,0,10,5"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtHue" Margin="0,0,0,5" TextChanged="HsvTextBox_TextChanged"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="S:" VerticalAlignment="Center" Margin="0,0,10,5"/>
                        <TextBox Grid.Row="1" Grid.Column="1" x:Name="txtSaturation" Margin="0,0,0,5" TextChanged="HsvTextBox_TextChanged"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="V:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtValue" TextChanged="HsvTextBox_TextChanged"/>
                    </Grid>
                </GroupBox>

                <!-- كود اللون -->
                <GroupBox Grid.Row="3" Header="كود اللون" Margin="0,0,0,15">
                    <TextBox x:Name="txtHexColor" FontFamily="Consolas" TextChanged="HexTextBox_TextChanged"/>
                </GroupBox>

                <!-- الألوان المحفوظة -->
                <GroupBox Grid.Row="4" Header="الألوان المحفوظة">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <WrapPanel x:Name="savedColorsPanel" Orientation="Horizontal">
                            <!-- سيتم إضافة الألوان المحفوظة هنا برمجياً -->
                        </WrapPanel>
                    </ScrollViewer>
                </GroupBox>
            </Grid>
        </Grid>

        <!-- الألوان السريعة -->
        <GroupBox Grid.Row="2" Header="الألوان السريعة" Margin="0,20,0,15">
            <WrapPanel x:Name="quickColorsPanel" Orientation="Horizontal" HorizontalAlignment="Center">
                <!-- سيتم إضافة الألوان السريعة هنا برمجياً -->
            </WrapPanel>
        </GroupBox>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button Content="حفظ اللون" Width="100" Height="35" Margin="5" 
                    Click="SaveColor_Click" Style="{StaticResource MaterialDesignRaisedButton}"/>
            <Button Content="موافق" Width="100" Height="35" Margin="5" 
                    Click="OK_Click" IsDefault="True" Style="{StaticResource MaterialDesignRaisedButton}"/>
            <Button Content="إلغاء" Width="100" Height="35" Margin="5" 
                    Click="Cancel_Click" IsCancel="True" Style="{StaticResource MaterialDesignOutlinedButton}"/>
        </StackPanel>
    </Grid>

</Window>