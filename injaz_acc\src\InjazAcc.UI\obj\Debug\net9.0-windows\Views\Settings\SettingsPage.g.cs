﻿#pragma checksum "..\..\..\..\..\Views\Settings\SettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AF570CE42C8E3CECDA8340A4DD4BDFFE72CF8607"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using InjazAcc.UI.Views.Settings;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace InjazAcc.UI.Views.Settings {
    
    
    /// <summary>
    /// SettingsPage
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 61 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCompanyName;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtLegalName;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtTaxNumber;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCommercialRegister;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpEstablishmentDate;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAddress;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPhone;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtEmail;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtWebsite;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtLogoPath;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbCurrency;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCurrencySymbol;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSaveCompanyInfo;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbPrimaryColor;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbSecondaryColor;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbTheme;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border colorPreview;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop primaryGradient;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.GradientStop secondaryGradient;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBackgroundColor;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTextColor;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBorderColor;
        
        #line default
        #line hidden
        
        
        #line 292 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnButtonColor;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSelectionColor;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnWarningColor;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClassicTheme;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnModernTheme;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnWarmTheme;
        
        #line default
        #line hidden
        
        
        #line 328 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCoolTheme;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnNatureTheme;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnElegantTheme;
        
        #line default
        #line hidden
        
        
        #line 340 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnApplyColors;
        
        #line default
        #line hidden
        
        
        #line 342 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSaveColorSettings;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnResetColors;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal InjazAcc.UI.Views.Settings.AdvancedColorControlPage advancedColorControl;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal InjazAcc.UI.Views.Settings.ColorTestPage colorTestPage;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkAutoBackup;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkShowWelcomeScreen;
        
        #line default
        #line hidden
        
        
        #line 406 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkAutoUpdate;
        
        #line default
        #line hidden
        
        
        #line 407 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkSendNotifications;
        
        #line default
        #line hidden
        
        
        #line 434 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSalesInvoicePrefix;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtPurchaseInvoicePrefix;
        
        #line default
        #line hidden
        
        
        #line 444 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDefaultTaxRate;
        
        #line default
        #line hidden
        
        
        #line 449 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtDefaultPaymentPeriod;
        
        #line default
        #line hidden
        
        
        #line 484 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbInvoicePrinter;
        
        #line default
        #line hidden
        
        
        #line 485 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefreshInvoicePrinters;
        
        #line default
        #line hidden
        
        
        #line 500 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbReportPrinter;
        
        #line default
        #line hidden
        
        
        #line 501 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefreshReportPrinters;
        
        #line default
        #line hidden
        
        
        #line 511 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbInvoicePaperSize;
        
        #line default
        #line hidden
        
        
        #line 522 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbReportPaperSize;
        
        #line default
        #line hidden
        
        
        #line 532 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkPrintLogo;
        
        #line default
        #line hidden
        
        
        #line 533 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkPrintPreview;
        
        #line default
        #line hidden
        
        
        #line 534 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkAutoPrint;
        
        #line default
        #line hidden
        
        
        #line 542 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSaveGeneralSettings;
        
        #line default
        #line hidden
        
        
        #line 543 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnResetSettings;
        
        #line default
        #line hidden
        
        
        #line 578 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchUser;
        
        #line default
        #line hidden
        
        
        #line 582 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddUser;
        
        #line default
        #line hidden
        
        
        #line 587 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgUsers;
        
        #line default
        #line hidden
        
        
        #line 632 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalUsers;
        
        #line default
        #line hidden
        
        
        #line 640 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtActiveUsers;
        
        #line default
        #line hidden
        
        
        #line 648 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtInactiveUsers;
        
        #line default
        #line hidden
        
        
        #line 671 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchRole;
        
        #line default
        #line hidden
        
        
        #line 675 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddRole;
        
        #line default
        #line hidden
        
        
        #line 680 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgRoles;
        
        #line default
        #line hidden
        
        
        #line 719 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalRoles;
        
        #line default
        #line hidden
        
        
        #line 734 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchPermission;
        
        #line default
        #line hidden
        
        
        #line 739 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgPermissions;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/InjazAcc.UI;component/views/settings/settingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 22 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.TabControl)(target)).SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TabControl_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.txtCompanyName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.txtLegalName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.txtTaxNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.txtCommercialRegister = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.dpEstablishmentDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.txtAddress = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.txtPhone = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.txtEmail = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.txtWebsite = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.txtLogoPath = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            
            #line 113 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnBrowseLogo_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.cmbCurrency = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.txtCurrencySymbol = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.btnSaveCompanyInfo = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnSaveCompanyInfo.Click += new System.Windows.RoutedEventHandler(this.btnSaveCompanyInfo_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.cmbPrimaryColor = ((System.Windows.Controls.ComboBox)(target));
            
            #line 197 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.cmbPrimaryColor.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbPrimaryColor_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.cmbSecondaryColor = ((System.Windows.Controls.ComboBox)(target));
            
            #line 213 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.cmbSecondaryColor.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbSecondaryColor_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.cmbTheme = ((System.Windows.Controls.ComboBox)(target));
            
            #line 228 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.cmbTheme.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.cmbTheme_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 19:
            this.colorPreview = ((System.Windows.Controls.Border)(target));
            return;
            case 20:
            this.primaryGradient = ((System.Windows.Media.GradientStop)(target));
            return;
            case 21:
            this.secondaryGradient = ((System.Windows.Media.GradientStop)(target));
            return;
            case 22:
            this.btnBackgroundColor = ((System.Windows.Controls.Button)(target));
            
            #line 275 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnBackgroundColor.Click += new System.Windows.RoutedEventHandler(this.btnBackgroundColor_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.btnTextColor = ((System.Windows.Controls.Button)(target));
            
            #line 281 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnTextColor.Click += new System.Windows.RoutedEventHandler(this.btnTextColor_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.btnBorderColor = ((System.Windows.Controls.Button)(target));
            
            #line 287 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnBorderColor.Click += new System.Windows.RoutedEventHandler(this.btnBorderColor_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.btnButtonColor = ((System.Windows.Controls.Button)(target));
            
            #line 293 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnButtonColor.Click += new System.Windows.RoutedEventHandler(this.btnButtonColor_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.btnSelectionColor = ((System.Windows.Controls.Button)(target));
            
            #line 299 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnSelectionColor.Click += new System.Windows.RoutedEventHandler(this.btnSelectionColor_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.btnWarningColor = ((System.Windows.Controls.Button)(target));
            
            #line 305 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnWarningColor.Click += new System.Windows.RoutedEventHandler(this.btnWarningColor_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.btnClassicTheme = ((System.Windows.Controls.Button)(target));
            
            #line 323 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnClassicTheme.Click += new System.Windows.RoutedEventHandler(this.btnClassicTheme_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.btnModernTheme = ((System.Windows.Controls.Button)(target));
            
            #line 325 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnModernTheme.Click += new System.Windows.RoutedEventHandler(this.btnModernTheme_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.btnWarmTheme = ((System.Windows.Controls.Button)(target));
            
            #line 327 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnWarmTheme.Click += new System.Windows.RoutedEventHandler(this.btnWarmTheme_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.btnCoolTheme = ((System.Windows.Controls.Button)(target));
            
            #line 329 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnCoolTheme.Click += new System.Windows.RoutedEventHandler(this.btnCoolTheme_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.btnNatureTheme = ((System.Windows.Controls.Button)(target));
            
            #line 331 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnNatureTheme.Click += new System.Windows.RoutedEventHandler(this.btnNatureTheme_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.btnElegantTheme = ((System.Windows.Controls.Button)(target));
            
            #line 333 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnElegantTheme.Click += new System.Windows.RoutedEventHandler(this.btnElegantTheme_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            this.btnApplyColors = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnApplyColors.Click += new System.Windows.RoutedEventHandler(this.btnApplyColors_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.btnSaveColorSettings = ((System.Windows.Controls.Button)(target));
            
            #line 343 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnSaveColorSettings.Click += new System.Windows.RoutedEventHandler(this.btnSaveColorSettings_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.btnResetColors = ((System.Windows.Controls.Button)(target));
            
            #line 345 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnResetColors.Click += new System.Windows.RoutedEventHandler(this.btnResetColors_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.advancedColorControl = ((InjazAcc.UI.Views.Settings.AdvancedColorControlPage)(target));
            return;
            case 38:
            this.colorTestPage = ((InjazAcc.UI.Views.Settings.ColorTestPage)(target));
            return;
            case 39:
            this.chkAutoBackup = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 40:
            this.chkShowWelcomeScreen = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 41:
            this.chkAutoUpdate = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 42:
            this.chkSendNotifications = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 43:
            this.txtSalesInvoicePrefix = ((System.Windows.Controls.TextBox)(target));
            return;
            case 44:
            this.txtPurchaseInvoicePrefix = ((System.Windows.Controls.TextBox)(target));
            return;
            case 45:
            this.txtDefaultTaxRate = ((System.Windows.Controls.TextBox)(target));
            return;
            case 46:
            this.txtDefaultPaymentPeriod = ((System.Windows.Controls.TextBox)(target));
            return;
            case 47:
            this.cmbInvoicePrinter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 48:
            this.btnRefreshInvoicePrinters = ((System.Windows.Controls.Button)(target));
            
            #line 486 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnRefreshInvoicePrinters.Click += new System.Windows.RoutedEventHandler(this.btnRefreshPrinters_Click);
            
            #line default
            #line hidden
            return;
            case 49:
            this.cmbReportPrinter = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 50:
            this.btnRefreshReportPrinters = ((System.Windows.Controls.Button)(target));
            
            #line 502 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnRefreshReportPrinters.Click += new System.Windows.RoutedEventHandler(this.btnRefreshPrinters_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.cmbInvoicePaperSize = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 52:
            this.cmbReportPaperSize = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 53:
            this.chkPrintLogo = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 54:
            this.chkPrintPreview = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 55:
            this.chkAutoPrint = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 56:
            this.btnSaveGeneralSettings = ((System.Windows.Controls.Button)(target));
            
            #line 542 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnSaveGeneralSettings.Click += new System.Windows.RoutedEventHandler(this.btnSaveGeneralSettings_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.btnResetSettings = ((System.Windows.Controls.Button)(target));
            
            #line 543 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnResetSettings.Click += new System.Windows.RoutedEventHandler(this.btnResetSettings_Click);
            
            #line default
            #line hidden
            return;
            case 58:
            this.txtSearchUser = ((System.Windows.Controls.TextBox)(target));
            
            #line 580 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.txtSearchUser.KeyUp += new System.Windows.Input.KeyEventHandler(this.txtSearchUser_KeyUp);
            
            #line default
            #line hidden
            return;
            case 59:
            this.btnAddUser = ((System.Windows.Controls.Button)(target));
            
            #line 583 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnAddUser.Click += new System.Windows.RoutedEventHandler(this.btnAddUser_Click);
            
            #line default
            #line hidden
            return;
            case 60:
            this.dgUsers = ((System.Windows.Controls.DataGrid)(target));
            
            #line 591 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.dgUsers.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.dgUsers_MouseDoubleClick);
            
            #line default
            #line hidden
            
            #line 592 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.dgUsers.CellEditEnding += new System.EventHandler<System.Windows.Controls.DataGridCellEditEndingEventArgs>(this.dgUsers_CellEditEnding);
            
            #line default
            #line hidden
            return;
            case 63:
            this.txtTotalUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 64:
            this.txtActiveUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 65:
            this.txtInactiveUsers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 66:
            this.txtSearchRole = ((System.Windows.Controls.TextBox)(target));
            
            #line 673 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.txtSearchRole.KeyUp += new System.Windows.Input.KeyEventHandler(this.txtSearchRole_KeyUp);
            
            #line default
            #line hidden
            return;
            case 67:
            this.btnAddRole = ((System.Windows.Controls.Button)(target));
            
            #line 676 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.btnAddRole.Click += new System.Windows.RoutedEventHandler(this.btnAddRole_Click);
            
            #line default
            #line hidden
            return;
            case 68:
            this.dgRoles = ((System.Windows.Controls.DataGrid)(target));
            
            #line 684 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.dgRoles.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.dgRoles_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 72:
            this.txtTotalRoles = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 73:
            this.txtSearchPermission = ((System.Windows.Controls.TextBox)(target));
            
            #line 736 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            this.txtSearchPermission.KeyUp += new System.Windows.Input.KeyEventHandler(this.txtSearchPermission_KeyUp);
            
            #line default
            #line hidden
            return;
            case 74:
            this.dgPermissions = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 61:
            
            #line 605 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnEditUser_Click);
            
            #line default
            #line hidden
            break;
            case 62:
            
            #line 610 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDeleteUser_Click);
            
            #line default
            #line hidden
            break;
            case 69:
            
            #line 694 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnEditRole_Click);
            
            #line default
            #line hidden
            break;
            case 70:
            
            #line 699 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnManagePermissions_Click);
            
            #line default
            #line hidden
            break;
            case 71:
            
            #line 704 "..\..\..\..\..\Views\Settings\SettingsPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.btnDeleteRole_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

