using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using InjazAcc.UI.Helpers;
using InjazAcc.UI.Views.Shared;

namespace InjazAcc.UI.Views.Settings
{
    /// <summary>
    /// صفحة ألوان الجداول والرسوم البيانية
    /// </summary>
    public partial class TableAndChartColorsPage : UserControl
    {
        private ObservableCollection<PreviewDataItem> _previewData;

        public TableAndChartColorsPage()
        {
            InitializeComponent();
            LoadCurrentSettings();
            InitializePreviewData();
            DrawChartPreview();
        }

        /// <summary>
        /// تحميل الإعدادات الحالية
        /// </summary>
        private void LoadCurrentSettings()
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                DataContext = settings;
                
                // تحديث ألوان الأزرار
                UpdateButtonColors();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث ألوان الأزرار
        /// </summary>
        private void UpdateButtonColors()
        {
            try
            {
                var settings = AdvancedColorManager.CurrentSettings;
                
                // تحديث ألوان الجداول
                UpdateButtonColor(btnTableHeaderColor, settings.TableHeaderColor);
                UpdateButtonColor(btnTableRowEvenColor, settings.TableRowEvenColor);
                UpdateButtonColor(btnTableRowOddColor, settings.TableRowOddColor);
                UpdateButtonColor(btnTableBorderColor, settings.TableBorderColor);
                UpdateButtonColor(btnTableSelectedRowColor, settings.TableSelectedRowColor);
                UpdateButtonColor(btnTableHoverRowColor, settings.TableHoverRowColor);
                
                // تحديث ألوان الرسوم البيانية
                UpdateButtonColor(btnChart1Color, settings.Chart1Color);
                UpdateButtonColor(btnChart2Color, settings.Chart2Color);
                UpdateButtonColor(btnChart3Color, settings.Chart3Color);
                UpdateButtonColor(btnChart4Color, settings.Chart4Color);
                UpdateButtonColor(btnChart5Color, settings.Chart5Color);
                UpdateButtonColor(btnChart6Color, settings.Chart6Color);
                UpdateButtonColor(btnChart7Color, settings.Chart7Color);
                UpdateButtonColor(btnChart8Color, settings.Chart8Color);
                
                // تحديث ألوان الأقسام
                UpdateButtonColor(btnSalesColor, settings.SalesColor);
                UpdateButtonColor(btnPurchasesColor, settings.PurchasesColor);
                UpdateButtonColor(btnInventoryColor, settings.InventoryColor);
                UpdateButtonColor(btnCustomersColor, settings.CustomersColor);
                UpdateButtonColor(btnSuppliersColor, settings.SuppliersColor);
                UpdateButtonColor(btnAccountingColor, settings.AccountingColor);
                UpdateButtonColor(btnReportsColor, settings.ReportsColor);
                UpdateButtonColor(btnSettingsColor, settings.SettingsColor);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث ألوان الأزرار: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث لون زر واحد
        /// </summary>
        private void UpdateButtonColor(Button button, string colorValue)
        {
            try
            {
                if (button != null && !string.IsNullOrEmpty(colorValue))
                {
                    var color = (Color)ColorConverter.ConvertFromString(colorValue);
                    button.Background = new SolidColorBrush(color);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث لون الزر: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة بيانات المعاينة
        /// </summary>
        private void InitializePreviewData()
        {
            try
            {
                _previewData = new ObservableCollection<PreviewDataItem>
                {
                    new PreviewDataItem { Id = 1, Name = "منتج أ", Type = "مبيعات", Amount = 1500.00m, Date = "2024-01-15" },
                    new PreviewDataItem { Id = 2, Name = "منتج ب", Type = "مشتريات", Amount = 800.00m, Date = "2024-01-16" },
                    new PreviewDataItem { Id = 3, Name = "منتج ج", Type = "مبيعات", Amount = 2200.00m, Date = "2024-01-17" },
                    new PreviewDataItem { Id = 4, Name = "منتج د", Type = "مشتريات", Amount = 950.00m, Date = "2024-01-18" },
                    new PreviewDataItem { Id = 5, Name = "منتج هـ", Type = "مبيعات", Amount = 1750.00m, Date = "2024-01-19" }
                };

                previewDataGrid.ItemsSource = _previewData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة بيانات المعاينة: {ex.Message}");
            }
        }

        /// <summary>
        /// رسم معاينة الرسم البياني
        /// </summary>
        private void DrawChartPreview()
        {
            try
            {
                chartPreview.Children.Clear();
                
                var settings = AdvancedColorManager.CurrentSettings;
                var colors = new string[]
                {
                    settings.Chart1Color, settings.Chart2Color, settings.Chart3Color, settings.Chart4Color,
                    settings.Chart5Color, settings.Chart6Color, settings.Chart7Color, settings.Chart8Color
                };

                var data = new double[] { 25, 35, 20, 15, 30, 40, 18, 22 };
                var labels = new string[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس" };

                double barWidth = 30;
                double spacing = 10;
                double maxHeight = 150;
                double maxValue = 40;

                for (int i = 0; i < data.Length; i++)
                {
                    // رسم العمود
                    var barHeight = (data[i] / maxValue) * maxHeight;
                    var rect = new Rectangle
                    {
                        Width = barWidth,
                        Height = barHeight,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString(colors[i]))
                    };

                    Canvas.SetLeft(rect, i * (barWidth + spacing) + 20);
                    Canvas.SetTop(rect, maxHeight - barHeight + 20);
                    chartPreview.Children.Add(rect);

                    // إضافة التسمية
                    var label = new TextBlock
                    {
                        Text = labels[i],
                        FontSize = 10,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    Canvas.SetLeft(label, i * (barWidth + spacing) + 20);
                    Canvas.SetTop(label, maxHeight + 25);
                    chartPreview.Children.Add(label);

                    // إضافة القيمة
                    var value = new TextBlock
                    {
                        Text = data[i].ToString(),
                        FontSize = 9,
                        Foreground = Brushes.White,
                        FontWeight = FontWeights.Bold,
                        HorizontalAlignment = HorizontalAlignment.Center
                    };

                    Canvas.SetLeft(value, i * (barWidth + spacing) + 25);
                    Canvas.SetTop(value, maxHeight - barHeight + 25);
                    chartPreview.Children.Add(value);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في رسم معاينة الرسم البياني: {ex.Message}");
            }
        }

        /// <summary>
        /// معالج النقر على أزرار الألوان
        /// </summary>
        private void ColorButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is string colorProperty)
                {
                    var colorPicker = new ColorPickerWindow();
                    
                    // تعيين اللون الحالي
                    if (button.Background is SolidColorBrush brush)
                    {
                        colorPicker.SelectedColor = brush.Color;
                    }
                    
                    if (colorPicker.ShowDialog() == true)
                    {
                        var newColor = colorPicker.SelectedColor.ToString();
                        
                        // تحديث اللون في المدير
                        AdvancedColorManager.UpdateColor(colorProperty, newColor);
                        
                        // تحديث الزر
                        button.Background = new SolidColorBrush(colorPicker.SelectedColor);
                        
                        // تحديث مربع النص المقابل
                        UpdateTextBoxValue(colorProperty, newColor);
                        
                        // تطبيق الألوان
                        AdvancedColorManager.ApplyAllColors();
                        
                        // تحديث المعاينات
                        if (colorProperty.StartsWith("Chart"))
                        {
                            DrawChartPreview();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار اللون: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج تغيير النص في مربعات الألوان
        /// </summary>
        private void ColorTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (sender is TextBox textBox && textBox.Tag is string colorProperty)
                {
                    var colorValue = textBox.Text;
                    
                    if (IsValidColor(colorValue))
                    {
                        // تحديث اللون في المدير
                        AdvancedColorManager.UpdateColor(colorProperty, colorValue);
                        
                        // تحديث الزر المقابل
                        UpdateButtonColorByProperty(colorProperty, colorValue);
                        
                        // تطبيق الألوان
                        AdvancedColorManager.ApplyAllColors();
                        
                        // تحديث المعاينات
                        if (colorProperty.StartsWith("Chart"))
                        {
                            DrawChartPreview();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث اللون من النص: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث قيمة مربع النص
        /// </summary>
        private void UpdateTextBoxValue(string colorProperty, string colorValue)
        {
            try
            {
                var textBoxName = $"txt{colorProperty}";
                var textBox = FindName(textBoxName) as TextBox;
                if (textBox != null)
                {
                    textBox.Text = colorValue;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث مربع النص: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث لون الزر بناءً على خاصية اللون
        /// </summary>
        private void UpdateButtonColorByProperty(string colorProperty, string colorValue)
        {
            try
            {
                var buttonName = $"btn{colorProperty}";
                var button = FindName(buttonName) as Button;
                if (button != null)
                {
                    UpdateButtonColor(button, colorValue);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث لون الزر: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من صحة اللون
        /// </summary>
        private bool IsValidColor(string colorValue)
        {
            try
            {
                ColorConverter.ConvertFromString(colorValue);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    /// <summary>
    /// عنصر بيانات المعاينة
    /// </summary>
    public class PreviewDataItem
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Date { get; set; } = string.Empty;
    }
}