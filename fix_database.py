import os
import subprocess
import sys

def fix_database():
    """إصلاح مشكلة قاعدة البيانات"""
    print("🔧 جاري إصلاح مشكلة قاعدة البيانات...")
    
    # مسار قاعدة البيانات المحلية
    db_paths = [
        os.path.expanduser("~\\AppData\\Local\\Microsoft\\Microsoft SQL Server Local DB\\Instances\\MSSQLLocalDB"),
        "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Microsoft SQL Server Local DB\\Instances\\MSSQLLocalDB",
        os.path.join(os.getcwd(), "injaz_acc", "src", "InjazAcc.UI", "bin", "Debug", "net9.0-windows")
    ]
    
    # البحث عن ملفات قاعدة البيانات وحذفها
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"🔍 البحث في: {db_path}")
            for root, dirs, files in os.walk(db_path):
                for file in files:
                    if file.startswith("InjazAccDB") and (file.endswith(".mdf") or file.endswith(".ldf")):
                        file_path = os.path.join(root, file)
                        try:
                            os.remove(file_path)
                            print(f"✅ تم حذف: {file_path}")
                        except Exception as e:
                            print(f"⚠️ لا يمكن حذف {file_path}: {e}")
    
    # حذف ملفات الإعدادات المحلية
    settings_paths = [
        os.path.join(os.path.expanduser("~"), "AppData", "Roaming", "InjazAcc"),
        "settings.db"
    ]
    
    for settings_path in settings_paths:
        if os.path.exists(settings_path):
            try:
                if os.path.isfile(settings_path):
                    os.remove(settings_path)
                    print(f"✅ تم حذف ملف الإعدادات: {settings_path}")
                else:
                    import shutil
                    shutil.rmtree(settings_path)
                    print(f"✅ تم حذف مجلد الإعدادات: {settings_path}")
            except Exception as e:
                print(f"⚠️ لا يمكن حذف {settings_path}: {e}")
    
    print("✅ تم إصلاح مشكلة قاعدة البيانات")
    print("🔄 يمكنك الآن تشغيل البرنامج مرة أخرى")

if __name__ == "__main__":
    fix_database()
    input("اضغط Enter للمتابعة...")