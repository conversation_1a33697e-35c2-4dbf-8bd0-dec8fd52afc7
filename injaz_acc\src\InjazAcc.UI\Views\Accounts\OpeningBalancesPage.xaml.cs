using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace InjazAcc.UI.Views.Accounts
{
    /// <summary>
    /// Interaction logic for OpeningBalancesPage.xaml
    /// </summary>
    public partial class OpeningBalancesPage : Page
    {
    private ObservableCollection<AccountOpeningBalance> _openingBalances;
    private readonly InjazAcc.DataAccess.InjazAccDbContext _dbContext;
        private double _totalDebit = 0;
        private double _totalCredit = 0;

        public OpeningBalancesPage()
        {
            InitializeComponent();
            _dbContext = new InjazAcc.DataAccess.InjazAccDbContext();
            LoadSampleData();
            UpdateTotals();
        }

        private void LoadSampleData()
        {
            try
            {
                // بيانات تجريبية لأرصدة أول المدة
                _openingBalances = new ObservableCollection<AccountOpeningBalance>
                {
                    new AccountOpeningBalance { AccountNumber = "1010", AccountName = "الصندوق", AccountType = "الأصول", DebitBalance = 50000, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "1020", AccountName = "البنك", AccountType = "الأصول", DebitBalance = 150000, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "1030", AccountName = "المخزون", AccountType = "الأصول", DebitBalance = 200000, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "1040", AccountName = "العملاء", AccountType = "الأصول", DebitBalance = 75000, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "1050", AccountName = "أثاث ومعدات", AccountType = "الأصول", DebitBalance = 120000, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "2010", AccountName = "الموردين", AccountType = "الخصوم", DebitBalance = 0, CreditBalance = 85000 },
                    new AccountOpeningBalance { AccountNumber = "2020", AccountName = "قروض قصيرة الأجل", AccountType = "الخصوم", DebitBalance = 0, CreditBalance = 100000 },
                    new AccountOpeningBalance { AccountNumber = "2030", AccountName = "مصروفات مستحقة", AccountType = "الخصوم", DebitBalance = 0, CreditBalance = 35000 },
                    new AccountOpeningBalance { AccountNumber = "3010", AccountName = "رأس المال", AccountType = "حقوق الملكية", DebitBalance = 0, CreditBalance = 350000 },
                    new AccountOpeningBalance { AccountNumber = "3020", AccountName = "الأرباح المحتجزة", AccountType = "حقوق الملكية", DebitBalance = 0, CreditBalance = 25000 },
                    new AccountOpeningBalance { AccountNumber = "4010", AccountName = "المبيعات", AccountType = "الإيرادات", DebitBalance = 0, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "4020", AccountName = "إيرادات أخرى", AccountType = "الإيرادات", DebitBalance = 0, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "5010", AccountName = "تكلفة المبيعات", AccountType = "المصروفات", DebitBalance = 0, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "5020", AccountName = "رواتب وأجور", AccountType = "المصروفات", DebitBalance = 0, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "5030", AccountName = "إيجارات", AccountType = "المصروفات", DebitBalance = 0, CreditBalance = 0 },
                    new AccountOpeningBalance { AccountNumber = "5040", AccountName = "مصروفات عمومية وإدارية", AccountType = "المصروفات", DebitBalance = 0, CreditBalance = 0 }
                };

                dgOpeningBalances.ItemsSource = _openingBalances;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTotals()
        {
            try
            {
                _totalDebit = 0;
                _totalCredit = 0;

                foreach (var balance in _openingBalances)
                {
                    _totalDebit += balance.DebitBalance;
                    _totalCredit += balance.CreditBalance;
                }

                txtTotalDebit.Text = _totalDebit.ToString("N2") + " ر.س";
                txtTotalCredit.Text = _totalCredit.ToString("N2") + " ر.س";

                double difference = _totalDebit - _totalCredit;
                txtDifference.Text = Math.Abs(difference).ToString("N2") + " ر.س";

                // تغيير لون الفرق حسب القيمة
                if (Math.Abs(difference) < 0.01) // تقريبًا صفر (متوازن)
                {
                    var border = txtDifference.Parent as FrameworkElement;
                    while (border != null && !(border is Border))
                    {
                        border = border.Parent as FrameworkElement;
                    }
                    if (border is Border borderElement)
                    {
                        borderElement.Background = System.Windows.Media.Brushes.Green;
                    }
                }
                else
                {
                    var border = txtDifference.Parent as FrameworkElement;
                    while (border != null && !(border is Border))
                    {
                        border = border.Parent as FrameworkElement;
                    }
                    if (border is Border borderElement)
                    {
                        borderElement.Background = System.Windows.Media.Brushes.Red;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحديث الإجماليات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string searchText = txtSearch.Text.Trim();
                string accountType = (cmbAccountType.SelectedItem as ComboBoxItem)?.Content.ToString();

                if (string.IsNullOrEmpty(searchText) && (accountType == "الكل" || accountType == null))
                {
                    dgOpeningBalances.ItemsSource = _openingBalances;
                    return;
                }

                var filteredBalances = new ObservableCollection<AccountOpeningBalance>();

                foreach (var balance in _openingBalances)
                {
                    bool typeMatch = accountType == "الكل" || accountType == null || balance.AccountType == accountType;
                    bool textMatch = string.IsNullOrEmpty(searchText) ||
                                    balance.AccountNumber.Contains(searchText) ||
                                    balance.AccountName.Contains(searchText);

                    if (typeMatch && textMatch)
                    {
                        filteredBalances.Add(balance);
                    }
                }

                dgOpeningBalances.ItemsSource = filteredBalances;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء البحث: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnShowAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                txtSearch.Text = "";
                cmbAccountType.SelectedIndex = 0;
                dgOpeningBalances.ItemsSource = _openingBalances;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnSaveChanges_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateTotals();

                if (Math.Abs(_totalDebit - _totalCredit) > 0.01)
                {
                    MessageBox.Show("لا يمكن حفظ الأرصدة. يجب أن تكون الأرصدة المدينة مساوية للأرصدة الدائنة.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // حذف الأرصدة القديمة
                foreach (var old in _dbContext.Set<AccountOpeningBalanceDb>())
                    _dbContext.Remove(old);

                // إضافة الأرصدة الجديدة
                foreach (var balance in _openingBalances)
                {
                    var dbBalance = new AccountOpeningBalanceDb
                    {
                        AccountNumber = balance.AccountNumber,
                        AccountName = balance.AccountName,
                        AccountType = balance.AccountType,
                        DebitBalance = balance.DebitBalance,
                        CreditBalance = balance.CreditBalance
                    };
                    _dbContext.Add(dbBalance);
                }
                _dbContext.SaveChanges();

                MessageBox.Show("تم حفظ أرصدة أول المدة بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnPostBalances_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                UpdateTotals();

                if (Math.Abs(_totalDebit - _totalCredit) > 0.01)
                {
                    MessageBox.Show("لا يمكن ترحيل الأرصدة. يجب أن تكون الأرصدة المدينة مساوية للأرصدة الدائنة.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBox.Show("تم ترحيل أرصدة أول المدة بنجاح", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء ترحيل الأرصدة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DebitBalance_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (textBox != null && textBox.DataContext is AccountOpeningBalance balance)
                {
                    if (double.TryParse(textBox.Text, out double value))
                    {
                        balance.DebitBalance = value;

                        // إذا كان هناك قيمة مدينة، نجعل القيمة الدائنة صفر
                        if (value > 0)
                        {
                            balance.CreditBalance = 0;
                        }

                        UpdateTotals();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreditBalance_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                var textBox = sender as TextBox;
                if (textBox != null && textBox.DataContext is AccountOpeningBalance balance)
                {
                    if (double.TryParse(textBox.Text, out double value))
                    {
                        balance.CreditBalance = value;

                        // إذا كان هناك قيمة دائنة، نجعل القيمة المدينة صفر
                        if (value > 0)
                        {
                            balance.DebitBalance = 0;
                        }

                        UpdateTotals();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnBackToAccounts_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة إلى صفحة الحسابات الرئيسية
                if (NavigationService != null)
                {
                    NavigationService.Navigate(new AccountsPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن العودة إلى صفحة الحسابات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء العودة للصفحة الرئيسية: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class AccountOpeningBalance
    {
    public string? AccountNumber { get; set; }
    public string? AccountName { get; set; }
    public string? AccountType { get; set; }
    public double DebitBalance { get; set; }
    public double CreditBalance { get; set; }
    }

    // كلاس قاعدة البيانات لأرصدة أول المدة
    public class AccountOpeningBalanceDb
    {
    public int Id { get; set; }
    public string? AccountNumber { get; set; }
    public string? AccountName { get; set; }
    public string? AccountType { get; set; }
    public double DebitBalance { get; set; }
    public double CreditBalance { get; set; }
    }
}
