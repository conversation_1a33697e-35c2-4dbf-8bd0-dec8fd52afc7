<Window x:Class="InjazAcc.UI.Views.Dialogs.AddProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="إضافة منتج جديد" Height="650" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="إضافة منتج جديد" 
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- محتوى النافذة -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- كود المنتج -->
                <TextBox x:Name="txtProductCode" 
                         materialDesign:HintAssist.Hint="كود المنتج"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- اسم المنتج -->
                <TextBox x:Name="txtProductName" 
                         materialDesign:HintAssist.Hint="اسم المنتج *"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- الوصف -->
                <TextBox x:Name="txtDescription" 
                         materialDesign:HintAssist.Hint="الوصف"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="2"
                         MaxLines="4"
                         Margin="0,10"/>

                <!-- الفئة -->
                <ComboBox x:Name="cmbCategory" 
                          materialDesign:HintAssist.Hint="الفئة"
                          Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                          Margin="0,10">
                    <ComboBoxItem Content="إلكترونيات"/>
                    <ComboBoxItem Content="ملابس"/>
                    <ComboBoxItem Content="أغذية"/>
                    <ComboBoxItem Content="أدوات منزلية"/>
                    <ComboBoxItem Content="كتب"/>
                    <ComboBoxItem Content="أخرى"/>
                </ComboBox>

                <!-- الوحدة -->
                <ComboBox x:Name="cmbUnit" 
                          materialDesign:HintAssist.Hint="الوحدة *"
                          Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                          Margin="0,10">
                    <ComboBoxItem Content="قطعة"/>
                    <ComboBoxItem Content="كيلو"/>
                    <ComboBoxItem Content="متر"/>
                    <ComboBoxItem Content="لتر"/>
                    <ComboBoxItem Content="علبة"/>
                    <ComboBoxItem Content="كرتونة"/>
                </ComboBox>

                <!-- سعر الشراء -->
                <TextBox x:Name="txtPurchasePrice" 
                         materialDesign:HintAssist.Hint="سعر الشراء"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- سعر البيع -->
                <TextBox x:Name="txtSalePrice" 
                         materialDesign:HintAssist.Hint="سعر البيع *"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- الكمية الافتتاحية -->
                <TextBox x:Name="txtOpeningQuantity" 
                         materialDesign:HintAssist.Hint="الكمية الافتتاحية"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- الحد الأدنى للمخزون -->
                <TextBox x:Name="txtMinimumStock" 
                         materialDesign:HintAssist.Hint="الحد الأدنى للمخزون"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- الحد الأقصى للمخزون -->
                <TextBox x:Name="txtMaximumStock" 
                         materialDesign:HintAssist.Hint="الحد الأقصى للمخزون"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- الباركود -->
                <TextBox x:Name="txtBarcode" 
                         materialDesign:HintAssist.Hint="الباركود"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         Margin="0,10"/>

                <!-- ملاحظات -->
                <TextBox x:Name="txtNotes" 
                         materialDesign:HintAssist.Hint="ملاحظات"
                         Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                         AcceptsReturn="True"
                         TextWrapping="Wrap"
                         MinLines="2"
                         MaxLines="4"
                         Margin="0,10"/>

                <!-- نشط -->
                <CheckBox x:Name="chkIsActive" 
                          Content="نشط" 
                          IsChecked="True"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Margin="0,15"/>

                <!-- يتبع المخزون -->
                <CheckBox x:Name="chkTrackInventory" 
                          Content="يتبع المخزون" 
                          IsChecked="True"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Margin="0,5"/>
            </StackPanel>
        </ScrollViewer>

        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="btnSave" Content="حفظ" 
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Background="{DynamicResource PrimaryHueMidBrush}"
                    Foreground="White"
                    Width="100" Margin="10,0"
                    Click="btnSave_Click"/>
            
            <Button x:Name="btnCancel" Content="إلغاء" 
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="100" Margin="10,0"
                    Click="btnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
