<Page x:Class="InjazAcc.UI.Views.Settings.ColorTestPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Background="{DynamicResource BackgroundBrush}"
      Title="اختبار الألوان">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock Text="اختبار الألوان الشامل" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="{DynamicResource PrimaryTextBrush}"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="معاينة شاملة لجميع الألوان والعناصر في البرنامج" 
                          FontSize="14" 
                          Foreground="{DynamicResource SecondaryTextBrush}"
                          HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>

            <!-- المحتوى الرئيسي -->
            <TabControl Grid.Row="1" Style="{StaticResource MaterialDesignTabControl}">
                
                <!-- تبويب الألوان الأساسية -->
                <TabItem Header="الألوان الأساسية">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- الألوان الرئيسية -->
                            <GroupBox Header="الألوان الرئيسية" Margin="0,0,0,20">
                                <WrapPanel Margin="10">
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource PrimaryBrush}">
                                        <TextBlock Text="اللون الأساسي" Foreground="{DynamicResource OnPrimaryTextBrush}" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource PrimaryDarkBrush}">
                                        <TextBlock Text="الأساسي الداكن" Foreground="{DynamicResource OnPrimaryTextBrush}" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource PrimaryLightBrush}">
                                        <TextBlock Text="الأساسي الفاتح" Foreground="{DynamicResource OnPrimaryTextBrush}" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource SecondaryBrush}">
                                        <TextBlock Text="اللون الثانوي" Foreground="{DynamicResource OnSecondaryTextBrush}" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource AccentBrush}">
                                        <TextBlock Text="لون التمييز" Foreground="{DynamicResource OnPrimaryTextBrush}" FontWeight="Bold"/>
                                    </Border>
                                </WrapPanel>
                            </GroupBox>

                            <!-- ألوان الحالة -->
                            <GroupBox Header="ألوان الحالة" Margin="0,0,0,20">
                                <WrapPanel Margin="10">
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource SuccessBrush}">
                                        <TextBlock Text="نجح" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource WarningBrush}">
                                        <TextBlock Text="تحذير" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource ErrorBrush}">
                                        <TextBlock Text="خطأ" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="20,10" CornerRadius="5" Background="{DynamicResource InfoBrush}">
                                        <TextBlock Text="معلومات" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                </WrapPanel>
                            </GroupBox>

                            <!-- ألوان النصوص -->
                            <GroupBox Header="ألوان النصوص" Margin="0,0,0,20">
                                <StackPanel Margin="10">
                                    <TextBlock Text="النص الأساسي" Foreground="{DynamicResource PrimaryTextBrush}" FontSize="16" Margin="0,5"/>
                                    <TextBlock Text="النص الثانوي" Foreground="{DynamicResource SecondaryTextBrush}" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="النص المعطل" Foreground="{DynamicResource DisabledTextBrush}" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="نص التلميح" Foreground="{DynamicResource HintTextBrush}" FontSize="12" Margin="0,5"/>
                                </StackPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب الأزرار -->
                <TabItem Header="الأزرار">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- الأزرار الأساسية -->
                            <GroupBox Header="الأزرار الأساسية" Margin="0,0,0,20">
                                <WrapPanel Margin="10">
                                    <Button Content="زر أساسي" Margin="5" Padding="15,8" 
                                           Background="{DynamicResource ButtonBackgroundBrush}"
                                           Foreground="{DynamicResource ButtonTextBrush}"/>
                                    <Button Content="زر ثانوي" Margin="5" Padding="15,8"
                                           Background="{DynamicResource SecondaryButtonBackgroundBrush}"
                                           Foreground="{DynamicResource SecondaryButtonTextBrush}"/>
                                    <Button Content="زر معطل" Margin="5" Padding="15,8" IsEnabled="False"
                                           Background="{DynamicResource ButtonDisabledBrush}"
                                           Foreground="{DynamicResource ButtonDisabledTextBrush}"/>
                                </WrapPanel>
                            </GroupBox>

                            <!-- أزرار الحالة -->
                            <GroupBox Header="أزرار الحالة" Margin="0,0,0,20">
                                <WrapPanel Margin="10">
                                    <Button Content="حفظ" Margin="5" Padding="15,8" 
                                           Background="{DynamicResource SuccessBrush}" Foreground="White"/>
                                    <Button Content="تحذير" Margin="5" Padding="15,8"
                                           Background="{DynamicResource WarningBrush}" Foreground="White"/>
                                    <Button Content="حذف" Margin="5" Padding="15,8"
                                           Background="{DynamicResource ErrorBrush}" Foreground="White"/>
                                    <Button Content="معلومات" Margin="5" Padding="15,8"
                                           Background="{DynamicResource InfoBrush}" Foreground="White"/>
                                </WrapPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب الجداول -->
                <TabItem Header="الجداول">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- جدول تجريبي -->
                            <GroupBox Header="جدول تجريبي" Margin="0,0,0,20">
                                <DataGrid x:Name="testDataGrid" Height="300" Margin="10" 
                                         AutoGenerateColumns="False" IsReadOnly="True"
                                         HeadersVisibility="Column" GridLinesVisibility="All"
                                         Background="{DynamicResource SurfaceBrush}"
                                         BorderBrush="{DynamicResource TableBorderBrush}">
                                    <DataGrid.ColumnHeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="{DynamicResource TableHeaderBrush}"/>
                                            <Setter Property="Foreground" Value="{DynamicResource PrimaryTextBrush}"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="Padding" Value="8"/>
                                        </Style>
                                    </DataGrid.ColumnHeaderStyle>
                                    <DataGrid.RowStyle>
                                        <Style TargetType="DataGridRow">
                                            <Setter Property="Background" Value="{DynamicResource TableRowEvenBrush}"/>
                                            <Style.Triggers>
                                                <Trigger Property="AlternationIndex" Value="1">
                                                    <Setter Property="Background" Value="{DynamicResource TableRowOddBrush}"/>
                                                </Trigger>
                                                <Trigger Property="IsSelected" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource TableSelectedRowBrush}"/>
                                                </Trigger>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource TableHoverRowBrush}"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGrid.RowStyle>
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80"/>
                                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                                        <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="120"/>
                                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount}" Width="100"/>
                                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="120"/>
                                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب الرسوم البيانية -->
                <TabItem Header="الرسوم البيانية">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- رسم بياني بالأعمدة -->
                            <GroupBox Header="رسم بياني بالأعمدة" Margin="0,0,0,20">
                                <Canvas x:Name="barChart" Height="250" Margin="10" Background="{DynamicResource SurfaceBrush}">
                                    <!-- سيتم رسم الأعمدة هنا برمجياً -->
                                </Canvas>
                            </GroupBox>

                            <!-- رسم بياني دائري -->
                            <GroupBox Header="رسم بياني دائري" Margin="0,0,0,20">
                                <Canvas x:Name="pieChart" Height="250" Margin="10" Background="{DynamicResource SurfaceBrush}">
                                    <!-- سيتم رسم الدائرة هنا برمجياً -->
                                </Canvas>
                            </GroupBox>

                            <!-- ألوان الرسوم البيانية -->
                            <GroupBox Header="ألوان الرسوم البيانية" Margin="0,0,0,20">
                                <WrapPanel Margin="10">
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart1Brush}">
                                        <TextBlock Text="اللون 1" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart2Brush}">
                                        <TextBlock Text="اللون 2" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart3Brush}">
                                        <TextBlock Text="اللون 3" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart4Brush}">
                                        <TextBlock Text="اللون 4" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart5Brush}">
                                        <TextBlock Text="اللون 5" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart6Brush}">
                                        <TextBlock Text="اللون 6" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart7Brush}">
                                        <TextBlock Text="اللون 7" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                    <Border Margin="5" Padding="15,10" CornerRadius="5" Background="{DynamicResource Chart8Brush}">
                                        <TextBlock Text="اللون 8" Foreground="White" FontWeight="Bold"/>
                                    </Border>
                                </WrapPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب الأقسام -->
                <TabItem Header="الأقسام">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- أقسام البرنامج -->
                            <GroupBox Header="أقسام البرنامج" Margin="0,0,0,20">
                                <WrapPanel Margin="10">
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource SalesBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="💰" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="المبيعات" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource PurchasesBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🛒" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="المشتريات" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource InventoryBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📦" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="المخزون" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource CustomersBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="👥" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="العملاء" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource SuppliersBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🏭" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="الموردين" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource AccountingBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📊" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="المحاسبة" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource ReportsBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📈" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="التقارير" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="20,15" CornerRadius="8" Background="{DynamicResource SettingsBrush}">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⚙️" FontSize="20" Margin="0,0,10,0"/>
                                            <TextBlock Text="الإعدادات" Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </WrapPanel>
                            </GroupBox>

                            <!-- قائمة جانبية تجريبية -->
                            <GroupBox Header="قائمة جانبية تجريبية" Margin="0,0,0,20">
                                <StackPanel x:Name="sidebarTest" Margin="10" Background="{DynamicResource SidebarBackgroundBrush}">
                                    <!-- سيتم إضافة عناصر القائمة هنا برمجياً -->
                                </StackPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

                <!-- تبويب العناصر المختلفة -->
                <TabItem Header="عناصر متنوعة">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="20">
                            
                            <!-- مربعات النص -->
                            <GroupBox Header="مربعات النص" Margin="0,0,0,20">
                                <StackPanel Margin="10">
                                    <TextBox Text="مربع نص عادي" Margin="0,5" 
                                            Background="{DynamicResource SurfaceBrush}"
                                            Foreground="{DynamicResource PrimaryTextBrush}"
                                            BorderBrush="{DynamicResource BorderBrush}"/>
                                    <TextBox Text="مربع نص مركز عليه" Margin="0,5" 
                                            Background="{DynamicResource SurfaceBrush}"
                                            Foreground="{DynamicResource PrimaryTextBrush}"
                                            BorderBrush="{DynamicResource FocusBorderBrush}"/>
                                    <TextBox Text="مربع نص معطل" Margin="0,5" IsEnabled="False"
                                            Background="{DynamicResource SurfaceBrush}"
                                            Foreground="{DynamicResource DisabledTextBrush}"
                                            BorderBrush="{DynamicResource BorderBrush}"/>
                                </StackPanel>
                            </GroupBox>

                            <!-- القوائم المنسدلة -->
                            <GroupBox Header="القوائم المنسدلة" Margin="0,0,0,20">
                                <StackPanel Margin="10">
                                    <ComboBox Margin="0,5" 
                                             Background="{DynamicResource SurfaceBrush}"
                                             Foreground="{DynamicResource PrimaryTextBrush}"
                                             BorderBrush="{DynamicResource BorderBrush}">
                                        <ComboBoxItem Content="خيار 1"/>
                                        <ComboBoxItem Content="خيار 2"/>
                                        <ComboBoxItem Content="خيار 3"/>
                                    </ComboBox>
                                </StackPanel>
                            </GroupBox>

                            <!-- البطاقات -->
                            <GroupBox Header="البطاقات" Margin="0,0,0,20">
                                <WrapPanel Margin="10">
                                    <Border Margin="5" Padding="15" CornerRadius="8" 
                                           Background="{DynamicResource CardBackgroundBrush}"
                                           BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1">
                                        <StackPanel>
                                            <TextBlock Text="بطاقة عادية" FontWeight="Bold" 
                                                      Foreground="{DynamicResource PrimaryTextBrush}"/>
                                            <TextBlock Text="محتوى البطاقة" 
                                                      Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Margin="5" Padding="15" CornerRadius="8" 
                                           Background="{DynamicResource CardBackgroundBrush}"
                                           BorderBrush="{DynamicResource PrimaryBrush}" BorderThickness="2">
                                        <StackPanel>
                                            <TextBlock Text="بطاقة مميزة" FontWeight="Bold" 
                                                      Foreground="{DynamicResource PrimaryTextBrush}"/>
                                            <TextBlock Text="محتوى مميز" 
                                                      Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        </StackPanel>
                                    </Border>
                                </WrapPanel>
                            </GroupBox>

                        </StackPanel>
                    </ScrollViewer>
                </TabItem>

            </TabControl>

        </Grid>
    </ScrollViewer>

</Page>